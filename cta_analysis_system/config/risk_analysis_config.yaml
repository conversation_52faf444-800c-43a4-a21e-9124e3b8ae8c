# Risk Analysis Module Configuration
#
# This configuration file contains all settings specific to the risk analysis module
# including VaR/ES calculations, stress testing, and risk validation parameters.

# ================================
# Risk Analysis Configuration
# ================================
risk_analysis:
  # Enable/disable risk analysis
  enabled: true
  
  # Risk analysis scope
  scope:
    # Include individual strategy risk
    include_strategy_risk: true
    
    # Include portfolio-level risk
    include_portfolio_risk: true
    
    # Include correlation analysis
    include_correlation_analysis: true

# ================================
# VaR and Expected Shortfall Configuration
# ================================
var_es_analysis:
  # Confidence levels for VaR/ES calculation
  confidence_levels:
    - 0.90
    - 0.95
    - 0.99
  
  # Calculation methods
  methods:
    - "historical"     # Historical simulation
    - "parametric"     # Parametric (normal distribution)
    - "monte_carlo"    # Monte Carlo simulation
  
  # Historical VaR settings
  historical:
    # Rolling window size for historical VaR
    rolling_window: 250
    
    # Minimum data points required
    min_data_points: 100
  
  # Parametric VaR settings
  parametric:
    # Distribution assumption
    distribution: "normal"  # "normal", "t_distribution", "skewed_t"
    
    # Enable GARCH modeling for volatility
    enable_garch: false
  
  # Monte Carlo settings
  monte_carlo:
    # Number of simulations
    num_simulations: 10000
    
    # Random seed for reproducibility
    random_seed: 42
    
    # Enable antithetic variates
    antithetic_variates: true

# ================================
# Stress Testing Configuration
# ================================
stress_testing:
  # Enable stress testing
  enabled: true
  
  # Stress test scenarios
  scenarios:
    # Historical stress scenarios
    historical:
      # Enable historical worst-case scenarios
      enabled: true
      
      # Time periods to analyze
      periods:
        - "worst_day"
        - "worst_week"
        - "worst_month"
        - "worst_quarter"
    
    # Monte Carlo stress testing
    monte_carlo:
      # Enable Monte Carlo stress testing
      enabled: true
      
      # Number of simulations
      num_simulations: 1000
      
      # Stress multipliers
      stress_multipliers: [2.0, 3.0, 5.0]
    
    # Custom stress scenarios
    custom:
      # Enable custom scenarios
      enabled: false
      
      # Custom scenario definitions
      scenarios:
        "market_crash":
          description: "Market crash scenario"
          return_shock: -0.20
          volatility_multiplier: 2.0
        "liquidity_crisis":
          description: "Liquidity crisis scenario"
          return_shock: -0.15
          volatility_multiplier: 1.5

# ================================
# Correlation Analysis Configuration
# ================================
correlation_analysis:
  # Enable correlation analysis
  enabled: true
  
  # Correlation calculation settings
  calculation:
    # Correlation method
    method: "pearson"  # "pearson", "spearman", "kendall"
    
    # Minimum overlap required for correlation calculation
    min_overlap: 30
    
    # Rolling correlation settings
    rolling:
      # Enable rolling correlation
      enabled: true
      
      # Rolling window size
      window_size: 60
  
  # Risk concentration detection
  concentration:
    # High correlation threshold
    high_correlation_threshold: 0.7
    
    # Medium correlation threshold
    medium_correlation_threshold: 0.5
    
    # Enable concentration alerts
    enable_alerts: true

# ================================
# Tail Risk Analysis Configuration
# ================================
tail_risk_analysis:
  # Enable tail risk analysis
  enabled: true
  
  # Tail risk metrics
  metrics:
    # Enable skewness calculation
    calculate_skewness: true
    
    # Enable kurtosis calculation
    calculate_kurtosis: true
    
    # Enable tail ratio calculation
    calculate_tail_ratio: true
    
    # Enable downside deviation
    calculate_downside_deviation: true
  
  # Tail ratio settings
  tail_ratio:
    # Upper percentile for tail ratio
    upper_percentile: 95
    
    # Lower percentile for tail ratio
    lower_percentile: 5
  
  # Extreme value analysis
  extreme_value_analysis:
    # Enable extreme value analysis
    enabled: false
    
    # Method for extreme value analysis
    method: "peaks_over_threshold"  # "block_maxima", "peaks_over_threshold"
    
    # Threshold for peaks over threshold method
    threshold_percentile: 95

# ================================
# Risk Validation Configuration
# ================================
risk_validation:
  # Enable risk validation
  enabled: true
  
  # Validation ranges for risk metrics
  validation_ranges:
    # Volatility validation range (annual)
    volatility_range: [0.05, 0.50]
    
    # Drawdown validation range
    drawdown_range: [-0.30, -0.005]
    
    # Sharpe ratio validation range
    sharpe_range: [-2.0, 3.0]
    
    # Return validation range (annual)
    return_range: [-0.50, 1.00]
  
  # Data quality requirements
  data_quality:
    # Minimum data points for validation
    min_data_points: 30
    
    # Maximum missing data ratio
    max_missing_ratio: 0.20
    
    # Enable outlier detection in validation
    enable_outlier_detection: true
  
  # Validation alerts
  alerts:
    # Enable validation alerts
    enabled: true
    
    # Alert on validation failures
    alert_on_failure: true
    
    # Alert on warnings
    alert_on_warning: true

# ================================
# Risk Adjusted Returns Configuration
# ================================
risk_adjusted_returns:
  # Enable risk-adjusted return calculation
  enabled: true
  
  # Risk-free rate settings
  risk_free_rate:
    # Annual risk-free rate
    annual_rate: 0.03
    
    # Source of risk-free rate
    source: "manual"  # "manual", "file", "api"
  
  # Metrics to calculate
  metrics:
    - "sharpe_ratio"
    - "sortino_ratio"
    - "calmar_ratio"
    - "information_ratio"
    - "treynor_ratio"
    - "jensen_alpha"
  
  # Benchmark settings (for relative metrics)
  benchmark:
    # Enable benchmark comparison
    enabled: false
    
    # Benchmark data source
    source: "file"  # "file", "api", "manual"
    
    # Benchmark file path
    file_path: "data/benchmark_returns.csv"

# ================================
# Portfolio Risk Configuration
# ================================
portfolio_risk:
  # Enable portfolio-level risk analysis
  enabled: true
  
  # Portfolio construction method
  construction_method: "equal_weight"  # "equal_weight", "market_cap", "risk_parity", "custom"
  
  # Custom weights (if construction_method = "custom")
  custom_weights: {}
  
  # Risk decomposition
  risk_decomposition:
    # Enable risk contribution analysis
    enabled: true
    
    # Decomposition method
    method: "marginal_var"  # "marginal_var", "component_var", "incremental_var"
  
  # Portfolio optimization
  optimization:
    # Enable portfolio optimization
    enabled: false
    
    # Optimization objective
    objective: "min_variance"  # "min_variance", "max_sharpe", "risk_parity"
    
    # Constraints
    constraints:
      # Maximum weight per asset
      max_weight: 0.3
      
      # Minimum weight per asset
      min_weight: 0.0
      
      # Sum of weights
      sum_weights: 1.0

# ================================
# Performance Configuration
# ================================
performance:
  # Enable performance optimizations
  optimizations_enabled: true
  
  # Caching settings
  caching:
    enabled: true
    expiry_time: 3600  # 1 hour
    max_size: 200      # MB
  
  # Parallel processing
  parallel_processing:
    # Enable parallel processing
    enabled: true
    
    # Number of worker processes
    num_workers: 4
    
    # Minimum data size for parallel processing
    min_data_size: 1000

# ================================
# Output Configuration
# ================================
output:
  # Output formats
  formats:
    - "dataframe"
    - "dict"
    - "json"
    - "excel"
  
  # Precision settings
  precision:
    # Decimal places for risk metrics
    risk_metrics_precision: 6
    
    # Decimal places for percentages
    percentage_precision: 4
    
    # Decimal places for correlations
    correlation_precision: 4
  
  # Visualization settings
  visualization:
    # Enable automatic chart generation
    enabled: true
    
    # Chart types to generate
    chart_types:
      - "var_es_timeseries"
      - "correlation_heatmap"
      - "risk_contribution"
      - "drawdown_periods"
    
    # Chart format
    format: "png"
    
    # Chart resolution
    dpi: 300

# ================================
# Logging Configuration
# ================================
logging:
  # Log level
  level: "INFO"
  
  # Enable detailed logging
  detailed_logging: true
  
  # Log risk calculations
  log_risk_calculations: true
  
  # Log validation results
  log_validation_results: true
  
  # Log performance metrics
  log_performance_metrics: true
