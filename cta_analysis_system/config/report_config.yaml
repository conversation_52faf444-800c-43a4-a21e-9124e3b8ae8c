# CTA策略分析系统 - 报告配置文件
#
# 此文件专门用于配置报告生成的相关参数
# 可以独立于主配置文件进行修改，提供更灵活的报告定制

# ================================
# 报告内容配置
# ================================
report_content:
  # 启用的分析模块
  enabled_modules:
    pnl_analysis: true           # 盈亏分析
    position_analysis: true      # 持仓分析
    return_rate_analysis: true   # 收益率分析
    performance_analysis: true   # 性能分析
    risk_analysis: true          # 风险分析
    contribution_analysis: true  # 贡献分析
    drawdown_analysis: true      # 回撤分析
  
  # 时间维度配置
  time_periods:
    # 默认生成的时间维度
    default_periods:
      - "daily"      # 本日
      - "weekly"     # 本周
      - "monthly"    # 本月
      - "yearly"     # 本年
    
    # 可选的时间维度
    optional_periods:
      - "quarterly"  # 本季度
      - "custom"     # 自定义时间段
    
    # 自定义时间段设置
    custom_period:
      enabled: true
      default_days: 30
      max_days: 365

# ================================
# Excel报告配置
# ================================
excel_report:
  # 工作表配置
  worksheets:
    # 每日表现摘要
    daily_summary:
      enabled: true
      name: "每日表现摘要"
      include_charts: true
      chart_types: ["line", "bar"]
    
    # 盈亏分析
    pnl_analysis:
      enabled: true
      name: "盈亏分析"
      include_pivot_tables: true
      include_custom_calculations: true
    
    # 持仓分析
    position_analysis:
      enabled: true
      name: "持仓分析"
      include_concentration: true
      include_contribution: true
      include_netting: true
    
    # 收益率分析
    return_rate_analysis:
      enabled: true
      name: "收益率分析"
      include_strategy_mapping: true
      include_validation_warnings: true
    
    # 策略概览
    strategy_overview:
      enabled: true
      name: "策略概览"
      include_performance_ranking: true
    
    # 风险指标
    risk_metrics:
      enabled: true
      name: "风险指标"
      include_var_es: true
      include_drawdown: true
      include_validation: true
    
    # 贡献分析
    contribution_analysis:
      enabled: true
      name: "贡献分析"
      include_waterfall_data: true
      use_modified_calculation: true
    
    # 行业分析
    industry_analysis:
      enabled: true
      name: "行业分析"
      include_concentration: true
      include_performance: true
    
    # 信号频率分析
    signal_freq_analysis:
      enabled: true
      name: "信号频率分析"
      include_return_mapping: true
  
  # 格式化配置
  formatting:
    # 条件格式化
    conditional_formatting:
      profit_loss:
        enabled: true
        positive_color: "#C6EFCE"  # 浅绿色
        negative_color: "#FFC7CE"  # 浅红色
        zero_color: "#FFFFFF"      # 白色
      
      performance_ranking:
        enabled: true
        top_color: "#00B050"       # 深绿色
        middle_color: "#FFFF00"    # 黄色
        bottom_color: "#FF0000"    # 红色
        top_threshold: 0.8         # 前20%
        bottom_threshold: 0.2      # 后20%
      
      concentration:
        enabled: true
        high_color: "#FF6B6B"      # 高集中度 - 红色
        medium_color: "#FFE66D"    # 中等集中度 - 黄色
        low_color: "#4ECDC4"       # 低集中度 - 青色
        high_threshold: 0.3        # 高集中度阈值
        medium_threshold: 0.15     # 中等集中度阈值
    
    # 数字格式
    number_formats:
      currency: "¥#,##0.00"       # 货币格式
      percentage: "0.00%"         # 百分比格式
      decimal_2: "0.00"           # 两位小数
      decimal_4: "0.0000"         # 四位小数
    
    # 表格样式
    table_style:
      header_style: "medium"      # 表头样式
      border_style: "thin"        # 边框样式
      font_name: "微软雅黑"        # 字体
      font_size: 10               # 字体大小

# ================================
# 可视化报告配置
# ================================
visualization_report:
  # 图表配置
  charts:
    # 累积收益曲线
    cumulative_returns:
      enabled: true
      include_benchmark: false
      include_drawdown_shading: true
      time_periods: ["monthly", "yearly"]
    
    # 收益分布图
    return_distribution:
      enabled: true
      chart_type: "histogram"     # histogram, kde, both
      bins: 50
      include_normal_fit: true
    
    # 瀑布图（贡献分析）
    waterfall_chart:
      enabled: true
      sort_by_contribution: true
      show_total: true
      max_categories: 15
    
    # 持仓集中度图
    concentration_chart:
      enabled: true
      chart_type: "pie"           # pie, bar, treemap
      max_categories: 10
    
    # 风险指标雷达图
    risk_radar:
      enabled: true
      metrics: ["sharpe_ratio", "max_drawdown", "volatility", "var_95"]
      normalize_metrics: true
    
    # 相关性热力图
    correlation_heatmap:
      enabled: true
      include_annotations: true
      color_scheme: "RdYlGn"
  
  # 仪表板配置
  dashboard:
    # 布局配置
    layout:
      grid_rows: 3
      grid_cols: 4
      figure_size: [20, 15]
      spacing: 0.3
    
    # 优先级配置
    chart_priority:
      high_priority:
        - "daily_pnl_summary"
        - "cumulative_returns"
        - "return_distribution"
      medium_priority:
        - "concentration_chart"
        - "risk_radar"
        - "waterfall_chart"
      low_priority:
        - "correlation_heatmap"

# ================================
# Markdown报告配置
# ================================
markdown_report:
  # 内容配置
  content:
    include_executive_summary: true
    include_charts: true
    include_tables: true
    include_recommendations: true
  
  # 图表配置
  charts:
    format: "png"               # png, svg, pdf
    dpi: 300
    include_captions: true
  
  # 表格配置
  tables:
    max_rows_per_table: 20
    include_totals: true
    format_numbers: true
  
  # 建议配置
  recommendations:
    include_risk_warnings: true
    include_allocation_suggestions: true
    include_performance_insights: true

# ================================
# HTML报告配置
# ================================
html_report:
  # 交互式功能
  interactive_features:
    enable_filtering: true
    enable_sorting: true
    enable_drill_down: true
  
  # 样式配置
  styling:
    theme: "bootstrap"          # bootstrap, material, custom
    color_scheme: "professional"
    responsive_design: true
  
  # 图表配置
  charts:
    library: "plotly"           # plotly, bokeh, d3
    interactive: true
    export_options: ["png", "svg", "html"]

# ================================
# 输出配置
# ================================
output:
  # 文件命名
  file_naming:
    prefix: "cta_analysis_report"
    include_timestamp: true
    timestamp_format: "%Y%m%d_%H%M%S"
    include_period_suffix: true
  
  # 目录结构
  directory_structure:
    base_dir: "reports"
    create_date_folders: true
    date_folder_format: "%Y%m%d"
    separate_by_type: true
  
  # 压缩配置
  compression:
    enabled: false
    format: "zip"               # zip, tar, gzip
    include_source_data: false

# ================================
# 质量控制配置
# ================================
quality_control:
  # 数据验证
  data_validation:
    check_completeness: true
    min_data_coverage: 0.8      # 最小数据覆盖率
    validate_calculations: true
  
  # 报告验证
  report_validation:
    check_file_integrity: true
    validate_chart_generation: true
    check_table_completeness: true
  
  # 错误处理
  error_handling:
    continue_on_error: true
    log_errors: true
    include_error_summary: true
