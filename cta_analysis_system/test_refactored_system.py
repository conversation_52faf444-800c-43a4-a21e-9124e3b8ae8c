#!/usr/bin/env python3
"""
重构系统测试脚本

测试新重构的CTA策略分析系统的各个模块功能
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_configuration_loading():
    """测试配置加载"""
    logger.info("Testing configuration loading...")
    
    try:
        from config.settings import config_manager
        config = config_manager.get_config()
        
        # 检查新增的配置项
        assert hasattr(config.analysis, 'pnl_analysis'), "PnL analysis config missing"
        assert hasattr(config.analysis, 'return_rate_calculation'), "Return rate calculation config missing"
        assert hasattr(config.position_analysis, 'concentration_analysis'), "Concentration analysis config missing"
        
        logger.info("✅ Configuration loading test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration loading test failed: {e}")
        return False


def test_data_loading():
    """测试数据加载"""
    logger.info("Testing data loading...")
    
    try:
        from data.data_loader import CTADataManager
        from config.settings import config_manager

        config = config_manager.get_config()
        data_manager = CTADataManager(config)
        
        # 加载数据
        success = data_manager.load_and_preprocess()
        
        if success:
            cta_data = data_manager.get_cta_data()
            position_data = data_manager.get_position_data()
            
            assert not cta_data.empty, "CTA data is empty"
            assert not position_data.empty, "Position data is empty"
            
            logger.info(f"✅ Data loading test passed - CTA: {len(cta_data)} records, Position: {len(position_data)} records")
            return True, cta_data, position_data
        else:
            logger.error("❌ Data loading failed")
            return False, None, None
            
    except Exception as e:
        logger.error(f"❌ Data loading test failed: {e}")
        return False, None, None


def test_pnl_analyzer(cta_data, config):
    """测试盈亏分析器"""
    logger.info("Testing PnL analyzer...")
    
    try:
        from analysis.pnl_analyzer import PnLAnalyzer
        
        analyzer = PnLAnalyzer(config)
        
        # 测试分类盈亏分析
        classified_results = analyzer.analyze_classified_pnl(
            cta_data, 
            primary_field='strategy_category'
        )
        
        assert 'pivot_results' in classified_results, "Pivot results missing"
        assert 'summary' in classified_results, "Summary missing"
        
        # 测试自定义计算
        custom_results = analyzer.calculate_custom_pnl(cta_data)
        
        logger.info("✅ PnL analyzer test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ PnL analyzer test failed: {e}")
        return False


def test_return_rate_calculator(cta_data, position_data, config):
    """测试收益率计算器"""
    logger.info("Testing return rate calculator...")
    
    try:
        from analysis.return_rate_calculator import ReturnRateCalculator
        
        calculator = ReturnRateCalculator(config)
        
        # 测试策略类别收益率计算
        strategy_returns = calculator.calculate_strategy_category_returns(cta_data, position_data)
        
        assert isinstance(strategy_returns, dict), "Strategy returns should be a dict"
        
        # 测试特定期货收益率计算
        futures_returns = calculator.calculate_specific_futures_returns(cta_data, position_data)
        
        assert isinstance(futures_returns, dict), "Futures returns should be a dict"
        
        # 测试策略信号收益率计算
        signal_returns = calculator.calculate_signal_returns(cta_data, position_data)
        
        assert isinstance(signal_returns, dict), "Signal returns should be a dict"
        
        logger.info("✅ Return rate calculator test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Return rate calculator test failed: {e}")
        return False


def test_position_analyzer(cta_data, position_data, config):
    """测试持仓分析器"""
    logger.info("Testing position analyzer...")
    
    try:
        from analysis.position_analyzer import PositionAnalyzer
        
        analyzer = PositionAnalyzer(config)
        
        # 测试当前持仓分析
        current_positions = analyzer.analyze_current_positions(cta_data, position_data)
        
        assert 'strategy_positions' in current_positions, "Strategy positions missing"
        assert 'industry_positions' in current_positions, "Industry positions missing"
        
        # 测试集中度分析
        concentration_results = analyzer.analyze_concentration(cta_data)
        
        assert isinstance(concentration_results, dict), "Concentration results should be a dict"
        
        # 测试持仓贡献分析
        contribution_results = analyzer.analyze_position_contribution(cta_data)
        
        assert isinstance(contribution_results, dict), "Contribution results should be a dict"
        
        logger.info("✅ Position analyzer test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Position analyzer test failed: {e}")
        return False


def test_comprehensive_analyzer(cta_data, position_data, config):
    """测试综合分析器"""
    logger.info("Testing comprehensive analyzer...")
    
    try:
        from analysis.comprehensive_analyzer import ComprehensiveAnalyzer
        
        analyzer = ComprehensiveAnalyzer(config)
        
        # 运行综合分析
        results = analyzer.run_comprehensive_analysis(cta_data, position_data)
        
        # 检查必要的分析结果
        expected_modules = [
            'pnl_analysis', 'position_analysis', 'return_rate_analysis',
            'performance_analysis', 'contribution_analysis'
        ]
        
        for module in expected_modules:
            assert module in results, f"Missing analysis module: {module}"
        
        # 检查综合摘要
        assert 'comprehensive_summary' in results, "Comprehensive summary missing"
        
        summary = results['comprehensive_summary']
        assert 'modules_completed' in summary, "Modules completed info missing"
        assert 'key_metrics' in summary, "Key metrics missing"
        
        logger.info("✅ Comprehensive analyzer test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Comprehensive analyzer test failed: {e}")
        return False


def test_report_generation(analysis_results, config):
    """测试报告生成"""
    logger.info("Testing report generation...")
    
    try:
        from reporting.excel_reporter import ExcelReporter
        
        reporter = ExcelReporter(config)
        
        # 生成Excel报告
        excel_file = reporter.generate_comprehensive_report(
            performance_results=analysis_results.get('performance_analysis', {}),
            risk_results=analysis_results.get('risk_analysis', {}),
            contribution_results=analysis_results.get('contribution_analysis', {})
        )
        
        if excel_file:
            logger.info(f"✅ Report generation test passed - Excel file: {excel_file}")
            return True
        else:
            logger.warning("⚠️ Report generation completed but no file returned")
            return True
            
    except Exception as e:
        logger.error(f"❌ Report generation test failed: {e}")
        return False


def main():
    """主测试函数"""
    logger.info("🚀 Starting refactored system tests")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 1. 测试配置加载
    test_results['configuration'] = test_configuration_loading()
    
    # 2. 测试数据加载
    data_success, cta_data, position_data = test_data_loading()
    test_results['data_loading'] = data_success
    
    if not data_success:
        logger.error("❌ Data loading failed, skipping remaining tests")
        return False
    
    # 获取配置
    from config.settings import config_manager
    config = config_manager.get_config()
    
    # 3. 测试各个分析器
    test_results['pnl_analyzer'] = test_pnl_analyzer(cta_data, config)
    test_results['return_rate_calculator'] = test_return_rate_calculator(cta_data, position_data, config)
    test_results['position_analyzer'] = test_position_analyzer(cta_data, position_data, config)
    
    # 4. 测试综合分析器
    comprehensive_success = test_comprehensive_analyzer(cta_data, position_data, config)
    test_results['comprehensive_analyzer'] = comprehensive_success
    
    # 5. 测试报告生成（如果综合分析成功）
    if comprehensive_success:
        from analysis.comprehensive_analyzer import ComprehensiveAnalyzer
        analyzer = ComprehensiveAnalyzer(config)
        analysis_results = analyzer.run_comprehensive_analysis(cta_data, position_data)
        test_results['report_generation'] = test_report_generation(analysis_results, config)
    else:
        test_results['report_generation'] = False
    
    # 输出测试结果
    logger.info("=" * 60)
    logger.info("🎯 TEST RESULTS SUMMARY")
    logger.info("=" * 60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name:25} : {status}")
        if result:
            passed_tests += 1
    
    logger.info("=" * 60)
    logger.info(f"📊 Overall Result: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        logger.info("🎉 All tests passed! Refactored system is working correctly.")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} tests failed. Please check the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
