#!/usr/bin/env python3
"""
Quantstats集成模块

该模块提供quantstats功能的封装，用于集成到我们的CTA分析系统中，
同时保持与现有数据结构和配置的兼容性。
"""

import quantstats as qs
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import warnings

# Suppress quantstats warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
qs.extend_pandas()

logger = logging.getLogger(__name__)


class QuantstatsIntegrator:
    """
    CTA分析系统的Quantstats集成封装器

    该类提供方法来用quantstats等效功能替换自定义实现，
    同时保持我们现有的接口。
    """

    def __init__(self, config=None):
        """初始化quantstats集成器"""
        self.config = config
        
    def calculate_performance_metrics(self, returns: pd.Series, 
                                    benchmark: pd.Series = None) -> Dict[str, float]:
        """
        Calculate comprehensive performance metrics using quantstats
        
        Args:
            returns: Return series
            benchmark: Optional benchmark series
            
        Returns:
            Dictionary of performance metrics
        """
        logger.info("使用quantstats计算性能指标")
        
        try:
            # Ensure returns is a pandas Series with datetime index
            if not isinstance(returns, pd.Series):
                returns = pd.Series(returns)
            
            # Remove any infinite or NaN values
            returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(returns) == 0:
                logger.warning("没有有效的收益率数据进行quantstats分析")
                return {}
            
            metrics = {}
            
            # Basic return metrics
            metrics['total_return'] = qs.stats.compsum(returns).iloc[-1] if len(returns) > 0 else 0
            metrics['cagr'] = qs.stats.cagr(returns)
            metrics['volatility'] = qs.stats.volatility(returns)
            metrics['sharpe_ratio'] = qs.stats.sharpe(returns)
            metrics['sortino_ratio'] = qs.stats.sortino(returns)
            metrics['calmar_ratio'] = qs.stats.calmar(returns)
            
            # Risk metrics
            metrics['max_drawdown'] = qs.stats.max_drawdown(returns)
            metrics['var_95'] = qs.stats.value_at_risk(returns, confidence=0.95)
            metrics['cvar_95'] = qs.stats.conditional_value_at_risk(returns, confidence=0.95)
            metrics['skewness'] = qs.stats.skew(returns)
            metrics['kurtosis'] = qs.stats.kurtosis(returns)
            
            # Win/Loss metrics
            metrics['win_rate'] = qs.stats.win_rate(returns)
            metrics['avg_win'] = qs.stats.avg_win(returns)
            metrics['avg_loss'] = qs.stats.avg_loss(returns)
            metrics['profit_factor'] = qs.stats.profit_factor(returns)
            metrics['payoff_ratio'] = qs.stats.payoff_ratio(returns)
            
            # Advanced metrics
            metrics['tail_ratio'] = qs.stats.tail_ratio(returns)
            metrics['common_sense_ratio'] = qs.stats.common_sense_ratio(returns)
            metrics['ulcer_index'] = qs.stats.ulcer_index(returns)
            metrics['serenity_index'] = qs.stats.serenity_index(returns)
            
            # Benchmark comparison if provided
            if benchmark is not None:
                benchmark = benchmark.replace([np.inf, -np.inf], np.nan).dropna()
                if len(benchmark) > 0:
                    # Align returns and benchmark
                    aligned_returns, aligned_benchmark = returns.align(benchmark, join='inner')
                    if len(aligned_returns) > 0:
                        metrics['beta'] = qs.stats.greeks(aligned_returns, aligned_benchmark).get('beta', np.nan)
                        metrics['alpha'] = qs.stats.greeks(aligned_returns, aligned_benchmark).get('alpha', np.nan)
                        metrics['information_ratio'] = qs.stats.information_ratio(aligned_returns, aligned_benchmark)
                        metrics['treynor_ratio'] = qs.stats.treynor_ratio(aligned_returns, aligned_benchmark)
            
            # Clean up any NaN or infinite values
            for key, value in metrics.items():
                if pd.isna(value) or np.isinf(value):
                    metrics[key] = 0.0
                    
            logger.info(f"Calculated {len(metrics)} performance metrics")
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def calculate_risk_metrics(self, returns: pd.Series, 
                             confidence_levels: List[float] = [0.95, 0.99]) -> Dict[str, Any]:
        """
        Calculate comprehensive risk metrics using quantstats
        
        Args:
            returns: Return series
            confidence_levels: VaR/CVaR confidence levels
            
        Returns:
            Dictionary of risk metrics
        """
        logger.info("Calculating risk metrics with quantstats")
        
        try:
            returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(returns) == 0:
                return {}
            
            risk_metrics = {}
            
            # Drawdown analysis
            drawdown_series = qs.stats.to_drawdown_series(returns)
            risk_metrics['max_drawdown'] = qs.stats.max_drawdown(returns)
            risk_metrics['avg_drawdown'] = drawdown_series.mean()
            risk_metrics['drawdown_periods'] = qs.stats.drawdown_details(returns)
            
            # Value at Risk and Expected Shortfall
            for conf in confidence_levels:
                risk_metrics[f'var_{int(conf*100)}'] = qs.stats.value_at_risk(returns, confidence=conf)
                risk_metrics[f'cvar_{int(conf*100)}'] = qs.stats.conditional_value_at_risk(returns, confidence=conf)
            
            # Volatility metrics
            risk_metrics['volatility'] = qs.stats.volatility(returns)
            risk_metrics['downside_volatility'] = qs.stats.volatility(returns[returns < 0])
            
            # Distribution metrics
            risk_metrics['skewness'] = qs.stats.skew(returns)
            risk_metrics['kurtosis'] = qs.stats.kurtosis(returns)
            risk_metrics['tail_ratio'] = qs.stats.tail_ratio(returns)
            
            # Risk-adjusted metrics
            risk_metrics['ulcer_index'] = qs.stats.ulcer_index(returns)
            risk_metrics['gain_to_pain_ratio'] = qs.stats.gain_to_pain_ratio(returns)
            
            # Clean up NaN values
            for key, value in risk_metrics.items():
                if isinstance(value, (int, float)) and (pd.isna(value) or np.isinf(value)):
                    risk_metrics[key] = 0.0
                    
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {}
    
    def generate_performance_plots(self, returns: pd.Series, 
                                 benchmark: pd.Series = None,
                                 output_dir: str = "output/quantstats_plots") -> Dict[str, str]:
        """
        Generate performance plots using quantstats
        
        Args:
            returns: Return series
            benchmark: Optional benchmark series
            output_dir: Output directory for plots
            
        Returns:
            Dictionary of generated plot file paths
        """
        logger.info("Generating performance plots with quantstats")
        
        try:
            import matplotlib.pyplot as plt
            import os
            
            os.makedirs(output_dir, exist_ok=True)
            
            returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(returns) == 0:
                return {}
            
            plot_files = {}
            
            # Cumulative returns plot
            plt.figure(figsize=(12, 6))
            qs.plots.returns(returns, benchmark=benchmark)
            plot_path = os.path.join(output_dir, "cumulative_returns.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files['cumulative_returns'] = plot_path
            
            # Drawdown plot
            plt.figure(figsize=(12, 6))
            qs.plots.drawdown(returns)
            plot_path = os.path.join(output_dir, "drawdown.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files['drawdown'] = plot_path
            
            # Monthly returns heatmap
            plt.figure(figsize=(12, 8))
            qs.plots.monthly_heatmap(returns)
            plot_path = os.path.join(output_dir, "monthly_heatmap.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files['monthly_heatmap'] = plot_path
            
            # Distribution plot
            plt.figure(figsize=(12, 6))
            qs.plots.distribution(returns)
            plot_path = os.path.join(output_dir, "distribution.png")
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            plt.close()
            plot_files['distribution'] = plot_path
            
            # Rolling metrics
            if len(returns) > 60:  # Need sufficient data for rolling metrics
                plt.figure(figsize=(12, 8))
                qs.plots.rolling_sharpe(returns)
                plot_path = os.path.join(output_dir, "rolling_sharpe.png")
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files['rolling_sharpe'] = plot_path
                
                plt.figure(figsize=(12, 8))
                qs.plots.rolling_volatility(returns)
                plot_path = os.path.join(output_dir, "rolling_volatility.png")
                plt.savefig(plot_path, dpi=300, bbox_inches='tight')
                plt.close()
                plot_files['rolling_volatility'] = plot_path
            
            logger.info(f"Generated {len(plot_files)} quantstats plots")
            return plot_files
            
        except Exception as e:
            logger.error(f"Error generating quantstats plots: {e}")
            return {}
    
    def generate_html_report(self, returns: pd.Series, 
                           benchmark: pd.Series = None,
                           output_file: str = "output/quantstats_report.html",
                           title: str = "CTA Strategy Performance Report") -> str:
        """
        Generate comprehensive HTML report using quantstats
        
        Args:
            returns: Return series
            benchmark: Optional benchmark series
            output_file: Output HTML file path
            title: Report title
            
        Returns:
            Path to generated HTML report
        """
        logger.info("Generating HTML report with quantstats")
        
        try:
            import os
            
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            returns = returns.replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(returns) == 0:
                logger.warning("No valid returns data for HTML report")
                return ""
            
            # Generate the HTML report
            qs.reports.html(returns, 
                          benchmark=benchmark,
                          output=output_file,
                          title=title)
            
            logger.info(f"Generated quantstats HTML report: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            return ""
    
    def get_replacement_mapping(self) -> Dict[str, str]:
        """
        Get mapping of custom functions that can be replaced with quantstats
        
        Returns:
            Dictionary mapping custom function names to quantstats equivalents
        """
        return {
            # Performance metrics
            'calculate_sharpe_ratio': 'qs.stats.sharpe',
            'calculate_sortino_ratio': 'qs.stats.sortino',
            'calculate_calmar_ratio': 'qs.stats.calmar',
            'calculate_max_drawdown': 'qs.stats.max_drawdown',
            'calculate_volatility': 'qs.stats.volatility',
            'calculate_var': 'qs.stats.value_at_risk',
            'calculate_cvar': 'qs.stats.conditional_value_at_risk',
            
            # Risk metrics
            'calculate_skewness': 'qs.stats.skew',
            'calculate_kurtosis': 'qs.stats.kurtosis',
            'calculate_tail_ratio': 'qs.stats.tail_ratio',
            'calculate_ulcer_index': 'qs.stats.ulcer_index',
            
            # Win/Loss metrics
            'calculate_win_rate': 'qs.stats.win_rate',
            'calculate_profit_factor': 'qs.stats.profit_factor',
            'calculate_payoff_ratio': 'qs.stats.payoff_ratio',
            
            # Visualization
            'plot_cumulative_returns': 'qs.plots.returns',
            'plot_drawdown': 'qs.plots.drawdown',
            'plot_monthly_heatmap': 'qs.plots.monthly_heatmap',
            'plot_distribution': 'qs.plots.distribution',
            'plot_rolling_sharpe': 'qs.plots.rolling_sharpe',
            'plot_rolling_volatility': 'qs.plots.rolling_volatility'
        }
