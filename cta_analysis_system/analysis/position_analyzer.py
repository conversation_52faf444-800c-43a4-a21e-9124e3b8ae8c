"""
CTA策略持仓分析模块
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class PositionAnalyzer:
    """持仓分析器"""
    
    def __init__(self, config):
        """初始化持仓分析器"""
        self.config = config

        # Handle both legacy and new configuration formats
        if hasattr(config, 'analysis') and hasattr(config.analysis, 'position_analysis'):
            self.position_config = config.analysis.position_analysis
        else:
            # Fallback to default configuration
            from types import SimpleNamespace
            self.position_config = SimpleNamespace()
            self.position_config.enabled = True
            self.position_config.concentration_analysis = {
                'analysis_targets': ["all_positions", "strategy_category"],
                'pivot_fields': ["industry", "symbol_category"],
                'calculation': {'metrics': ["position_amount", "concentration_ratio"]},
                'output': {'fields': ["symbol_category", "position_amount", "concentration_ratio"]}
            }
            self.position_config.contribution_analysis = {
                'analysis_targets': ["all_positions", "strategy_category"],
                'pivot_field': "industry",
                'calculation': {'method': "separate"},
                'output': {'fields': ["industry", "position_value", "contribution_ratio", "contribution_type"]}
            }
        
    def analyze_current_positions(self, cta_data: pd.DataFrame, position_data: pd.DataFrame) -> Dict[str, Any]:
        """分析当前持仓情况"""
        logger.info("Analyzing current positions...")
        
        # 获取最新日期的数据
        latest_date = cta_data[self.config.data.date_column].max()
        latest_cta_data = cta_data[cta_data[self.config.data.date_column] == latest_date]

        # Handle position data date column
        pos_date_col = 'date' if 'date' in position_data.columns else self.config.data.date_column
        latest_position_data = position_data[position_data[pos_date_col] == latest_date]
        
        if latest_cta_data.empty:
            logger.warning("No CTA data found for latest date")
            return {}
        
        # 按策略分组分析
        strategy_positions = self._analyze_strategy_positions(latest_cta_data, latest_position_data)
        
        # 行业持仓分析
        industry_positions = self._analyze_industry_positions(latest_cta_data)
        
        # 持仓集中度分析
        concentration_analysis = self._analyze_position_concentration(latest_cta_data)
        
        # 多空轧差分析
        netting_analysis = self._analyze_long_short_netting(latest_cta_data)
        
        return {
            "analysis_date": latest_date,
            "strategy_positions": strategy_positions,
            "industry_positions": industry_positions,
            "concentration_analysis": concentration_analysis,
            "netting_analysis": netting_analysis,
            "summary": self._generate_position_summary(strategy_positions, industry_positions)
        }
    
    def _analyze_strategy_positions(self, cta_data: pd.DataFrame, position_data: pd.DataFrame) -> Dict[str, Any]:
        """分析各策略持仓"""
        results = {}
        
        strategy_col = self.config.data.strategy_category_column
        industry_col = self.config.data.industry_column
        position_amount_col = self.config.data.position_amount_column
        
        for strategy in cta_data[strategy_col].unique():
            strategy_data = cta_data[cta_data[strategy_col] == strategy]
            
            # 获取策略权重
            strategy_weight = 1.0
            if not position_data.empty:
                # Handle strategy column in position data
                pos_strategy_col = 'strategy_category' if 'strategy_category' in position_data.columns else strategy_col
                strategy_position_data = position_data[position_data[pos_strategy_col] == strategy]
                if not strategy_position_data.empty:
                    pos_col = 'position' if 'position' in position_data.columns else self.config.data.position_column
                    strategy_weight = strategy_position_data[pos_col].iloc[0]
            
            # 按行业分析
            industry_analysis = {}
            for industry in strategy_data[industry_col].unique():
                industry_data = strategy_data[strategy_data[industry_col] == industry]
                
                # 计算行业内的持仓
                total_position_amount = industry_data[position_amount_col].sum()
                weighted_position_amount = total_position_amount * strategy_weight
                
                # 计算多空持仓（简化处理：正PnL视为多头，负PnL视为空头）
                long_positions = industry_data[industry_data[self.config.data.pnl_column] >= 0]
                short_positions = industry_data[industry_data[self.config.data.pnl_column] < 0]
                
                long_amount = long_positions[position_amount_col].sum() * strategy_weight
                short_amount = short_positions[position_amount_col].sum() * strategy_weight
                net_position = long_amount - short_amount
                
                industry_analysis[industry] = {
                    "total_position_amount": round(total_position_amount, 2),
                    "weighted_position_amount": round(weighted_position_amount, 2),
                    "long_amount": round(long_amount, 2),
                    "short_amount": round(short_amount, 2),
                    "net_position": round(net_position, 2),
                    "position_direction": "多头" if net_position > 0 else "空头" if net_position < 0 else "平衡",
                    "strategy_weight": strategy_weight
                }
            
            results[strategy] = {
                "industry_breakdown": industry_analysis,
                "total_strategy_position": sum([ind["weighted_position_amount"] for ind in industry_analysis.values()]),
                "strategy_weight": strategy_weight
            }
        
        return results
    
    def _analyze_industry_positions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """分析行业持仓分布"""
        results = {}
        
        industry_col = self.config.data.industry_column
        position_amount_col = self.config.data.position_amount_column
        pnl_col = self.config.data.pnl_column
        
        for industry in cta_data[industry_col].unique():
            industry_data = cta_data[cta_data[industry_col] == industry]
            
            total_position = industry_data[position_amount_col].sum()
            total_pnl = industry_data[pnl_col].sum()
            
            # 多空分析
            long_data = industry_data[industry_data[pnl_col] >= 0]
            short_data = industry_data[industry_data[pnl_col] < 0]
            
            long_position = long_data[position_amount_col].sum()
            short_position = short_data[position_amount_col].sum()
            net_position = long_position - short_position
            
            results[industry] = {
                "total_position": round(total_position, 2),
                "long_position": round(long_position, 2),
                "short_position": round(short_position, 2),
                "net_position": round(net_position, 2),
                "total_pnl": round(total_pnl, 2),
                "position_direction": "多头" if net_position > 0 else "空头" if net_position < 0 else "平衡"
            }
        
        return results
    
    def _analyze_position_concentration(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """分析持仓集中度"""
        position_amount_col = self.config.data.position_amount_column
        strategy_col = self.config.data.strategy_category_column
        industry_col = self.config.data.industry_column
        
        total_position = cta_data[position_amount_col].sum()
        
        # 策略集中度
        strategy_concentration = {}
        for strategy in cta_data[strategy_col].unique():
            strategy_position = cta_data[cta_data[strategy_col] == strategy][position_amount_col].sum()
            concentration = strategy_position / total_position if total_position > 0 else 0
            strategy_concentration[strategy] = round(concentration, 4)
        
        # 行业集中度
        industry_concentration = {}
        for industry in cta_data[industry_col].unique():
            industry_position = cta_data[cta_data[industry_col] == industry][position_amount_col].sum()
            concentration = industry_position / total_position if total_position > 0 else 0
            industry_concentration[industry] = round(concentration, 4)
        
        # 计算HHI指数
        strategy_hhi = sum([conc**2 for conc in strategy_concentration.values()])
        industry_hhi = sum([conc**2 for conc in industry_concentration.values()])
        
        return {
            "total_position": round(total_position, 2),
            "strategy_concentration": strategy_concentration,
            "industry_concentration": industry_concentration,
            "strategy_hhi": round(strategy_hhi, 4),
            "industry_hhi": round(industry_hhi, 4),
            "concentration_level": self._assess_concentration_level(strategy_hhi, industry_hhi)
        }
    
    def _analyze_long_short_netting(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """分析多空轧差"""
        netting_config = self.config.position_analysis.netting if hasattr(self.config.position_analysis, 'netting') else {}
        if not netting_config.get('enable_long_short_netting', True):
            return {"enabled": False}
        
        strategy_col = self.config.data.strategy_category_column
        industry_col = self.config.data.industry_column
        position_amount_col = self.config.data.position_amount_column
        pnl_col = self.config.data.pnl_column
        
        netting_results = {}
        
        for strategy in cta_data[strategy_col].unique():
            strategy_data = cta_data[cta_data[strategy_col] == strategy]
            strategy_netting = {}
            
            for industry in strategy_data[industry_col].unique():
                industry_data = strategy_data[strategy_data[industry_col] == industry]
                
                # 多头持仓（PnL >= 0）
                long_data = industry_data[industry_data[pnl_col] >= 0]
                long_position = long_data[position_amount_col].sum()
                
                # 空头持仓（PnL < 0）
                short_data = industry_data[industry_data[pnl_col] < 0]
                short_position = short_data[position_amount_col].sum()
                
                # 轧差计算
                net_position = long_position - short_position
                gross_position = long_position + short_position
                
                precision = netting_config.get('precision', 2)
                strategy_netting[industry] = {
                    "long_position": round(long_position, precision),
                    "short_position": round(short_position, precision),
                    "net_position": round(net_position, precision),
                    "gross_position": round(gross_position, precision),
                    "netting_ratio": round(abs(net_position) / gross_position if gross_position > 0 else 0, 4)
                }
            
            netting_results[strategy] = strategy_netting
        
        return {
            "enabled": True,
            "netting_results": netting_results,
            "summary": self._generate_netting_summary(netting_results)
        }
    
    def _assess_concentration_level(self, strategy_hhi: float, industry_hhi: float) -> str:
        """评估集中度水平"""
        thresholds_config = self.config.position_analysis.thresholds if hasattr(self.config.position_analysis, 'thresholds') else {}
        threshold = thresholds_config.get('concentration_threshold', 0.3)

        if strategy_hhi > threshold or industry_hhi > threshold:
            return "高集中度"
        elif strategy_hhi > threshold * 0.6 or industry_hhi > threshold * 0.6:
            return "中等集中度"
        else:
            return "低集中度"
    
    def _generate_position_summary(self, strategy_positions: Dict, industry_positions: Dict) -> Dict[str, Any]:
        """生成持仓摘要"""
        total_position = sum([pos["total_strategy_position"] for pos in strategy_positions.values()])
        
        # 最大策略持仓
        max_strategy = max(strategy_positions.items(), key=lambda x: x[1]["total_strategy_position"])
        
        # 最大行业持仓
        max_industry = max(industry_positions.items(), key=lambda x: x[1]["total_position"])
        
        return {
            "total_position": round(total_position, 2),
            "strategy_count": len(strategy_positions),
            "industry_count": len(industry_positions),
            "max_strategy": {
                "name": max_strategy[0],
                "position": max_strategy[1]["total_strategy_position"]
            },
            "max_industry": {
                "name": max_industry[0],
                "position": max_industry[1]["total_position"]
            }
        }
    
    def analyze_concentration(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        集中度分析

        Args:
            cta_data: CTA数据

        Returns:
            集中度分析结果
        """
        logger.info("Running concentration analysis")

        try:
            results = {}
            concentration_config = self.config.position_analysis.concentration_analysis

            # 分析所有持仓和不同strategy_category的数据
            for target in concentration_config.analysis_targets:
                if target == "all_positions":
                    target_data = cta_data.copy()
                    target_name = "all_positions"
                elif target == "strategy_category":
                    # 按策略类别分别分析
                    strategy_results = {}
                    for strategy in cta_data['strategy_category'].unique():
                        strategy_data = cta_data[cta_data['strategy_category'] == strategy]
                        strategy_results[strategy] = self._calculate_concentration_metrics(
                            strategy_data, concentration_config
                        )
                    results['strategy_category'] = strategy_results
                    continue

                if target == "all_positions":
                    results[target_name] = self._calculate_concentration_metrics(
                        target_data, concentration_config
                    )

            return results

        except Exception as e:
            logger.error(f"Concentration analysis failed: {e}")
            raise

    def analyze_position_contribution(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        持仓市值贡献比例分析

        Args:
            cta_data: CTA数据

        Returns:
            贡献比例分析结果
        """
        logger.info("Running position contribution analysis")

        try:
            results = {}
            contribution_config = self.config.position_analysis.contribution_analysis

            # 分析所有持仓和不同strategy_category的数据
            for target in contribution_config.analysis_targets:
                if target == "all_positions":
                    target_data = cta_data.copy()
                    target_name = "all_positions"
                elif target == "strategy_category":
                    # 按策略类别分别分析
                    strategy_results = {}
                    for strategy in cta_data['strategy_category'].unique():
                        strategy_data = cta_data[cta_data['strategy_category'] == strategy]
                        strategy_results[strategy] = self._calculate_contribution_metrics(
                            strategy_data, contribution_config
                        )
                    results['strategy_category'] = strategy_results
                    continue

                if target == "all_positions":
                    results[target_name] = self._calculate_contribution_metrics(
                        target_data, contribution_config
                    )

            return results

        except Exception as e:
            logger.error(f"Position contribution analysis failed: {e}")
            raise

    def _calculate_concentration_metrics(self, data: pd.DataFrame,
                                       concentration_config: Dict[str, Any]) -> Dict[str, Any]:
        """计算集中度指标"""
        try:
            results = {}

            for pivot_field in concentration_config.pivot_fields:
                if pivot_field not in data.columns:
                    continue

                # 按透视字段分组计算
                grouped = data.groupby(pivot_field)['position_amount'].sum()

                # 计算集中度比例（使用绝对值总和作为分母）
                total_abs_amount = grouped.abs().sum()
                concentration_ratios = grouped.abs() / total_abs_amount if total_abs_amount > 0 else grouped * 0

                # 生成输出数据
                output_data = []
                output_fields = concentration_config.get('output', {}).get('fields', ['category', 'position_amount', 'concentration_ratio'])
                for category, amount in grouped.items():
                    output_data.append({
                        output_fields[0]: category,  # symbol_category 或 industry
                        output_fields[1]: round(amount, 2),  # position_amount
                        output_fields[2]: round(concentration_ratios[category], 4)  # concentration_ratio
                    })

                # 排序
                output_config = concentration_config.get('output', {})
                sort_field = output_config.get('sort_by', 'concentration_ratio')
                sort_ascending = output_config.get('sort_ascending', False)
                output_data.sort(key=lambda x: x[sort_field], reverse=not sort_ascending)

                results[pivot_field] = {
                    'data': output_data,
                    'total_amount': round(total_abs_amount, 2),
                    'category_count': len(grouped)
                }

            return results

        except Exception as e:
            logger.error(f"Concentration metrics calculation failed: {e}")
            return {}

    def _calculate_contribution_metrics(self, data: pd.DataFrame,
                                      contribution_config: Dict[str, Any]) -> Dict[str, Any]:
        """计算贡献比例指标"""
        try:
            pivot_field = contribution_config.get('pivot_field', 'industry')

            if pivot_field not in data.columns:
                return {}

            # 按透视字段分组计算
            grouped = data.groupby(pivot_field)['position_amount'].sum()

            # 分离正负持仓
            positive_amounts = grouped[grouped > 0]
            negative_amounts = grouped[grouped < 0]

            # 计算贡献比例
            output_data = []

            calculation_config = contribution_config.get('calculation', {})
            if calculation_config.get('method', 'separate') == "separate":
                # 正向贡献
                positive_total = positive_amounts.sum() if len(positive_amounts) > 0 else 0
                for category, amount in positive_amounts.items():
                    contribution_ratio = amount / positive_total if positive_total > 0 else 0
                    output_data.append({
                        'industry': category,
                        'position_value': round(amount, 2),
                        'contribution_ratio': round(contribution_ratio, 4),
                        'contribution_type': 'positive'
                    })

                # 负向贡献
                negative_total = abs(negative_amounts.sum()) if len(negative_amounts) > 0 else 0
                for category, amount in negative_amounts.items():
                    contribution_ratio = abs(amount) / negative_total if negative_total > 0 else 0
                    output_data.append({
                        'industry': category,
                        'position_value': round(amount, 2),
                        'contribution_ratio': round(contribution_ratio, 4),
                        'contribution_type': 'negative'
                    })

            # 排序
            output_config = contribution_config.get('output', {})
            sort_field = output_config.get('sort_by', 'contribution_ratio')
            sort_ascending = output_config.get('sort_ascending', False)
            output_data.sort(key=lambda x: x[sort_field], reverse=not sort_ascending)

            return {
                'data': output_data,
                'positive_total': round(positive_amounts.sum(), 2) if len(positive_amounts) > 0 else 0,
                'negative_total': round(negative_amounts.sum(), 2) if len(negative_amounts) > 0 else 0,
                'category_count': len(grouped)
            }

        except Exception as e:
            logger.error(f"Contribution metrics calculation failed: {e}")
            return {}

    def _generate_netting_summary(self, netting_results: Dict) -> Dict[str, Any]:
        """生成轧差摘要"""
        total_net = 0
        total_gross = 0

        for strategy_data in netting_results.values():
            for industry_data in strategy_data.values():
                total_net += abs(industry_data["net_position"])
                total_gross += industry_data["gross_position"]

        overall_netting_ratio = total_net / total_gross if total_gross > 0 else 0

        return {
            "total_net_position": round(total_net, 2),
            "total_gross_position": round(total_gross, 2),
            "overall_netting_ratio": round(overall_netting_ratio, 4),
            "netting_efficiency": "高效" if overall_netting_ratio < 0.3 else "中等" if overall_netting_ratio < 0.7 else "低效"
        }
    
    def generate_position_report(self, position_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成格式化的持仓分析报告"""
        if not position_analysis:
            return []

        report_data = []
        strategy_positions = position_analysis.get("strategy_positions", {})

        # 获取配置
        thresholds_config = self.config.position_analysis.thresholds if hasattr(self.config.position_analysis, 'thresholds') else {}
        min_position_amount = thresholds_config.get('min_position_amount', 1.0)

        for strategy_name, strategy_data in strategy_positions.items():
            industry_breakdown = strategy_data.get("industry_breakdown", {})

            for industry, industry_data in industry_breakdown.items():
                # 过滤小额持仓
                if industry_data["weighted_position_amount"] < min_position_amount:
                    continue
                
                report_data.append({
                    "strategy_name": strategy_name,
                    "industry": industry,
                    "net_position": industry_data["net_position"],
                    "position_value": industry_data["weighted_position_amount"],
                    "position_direction": industry_data["position_direction"],
                    "long_amount": industry_data["long_amount"],
                    "short_amount": industry_data["short_amount"]
                })
        
        # 按配置排序
        output_format_config = self.config.position_analysis.output_format if hasattr(self.config.position_analysis, 'output_format') else {}
        sort_field = output_format_config.get('sort_by', 'position_value')
        sort_ascending = output_format_config.get('sort_ascending', False)
        
        if sort_field in ["position_value", "net_position"]:
            report_data.sort(key=lambda x: abs(x[sort_field]), reverse=not sort_ascending)
        
        return report_data
