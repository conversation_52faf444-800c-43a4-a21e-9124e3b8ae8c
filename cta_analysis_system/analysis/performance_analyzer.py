"""
CTA策略表现分析模块

Enhanced with Quantstats integration for comprehensive performance metrics.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
from scipy import stats

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import CTAConfig
from utils.time_window_calculator import TimeWindowCalculator

# Import quantstats integration and output manager
try:
    from .quantstats_integration import QuantstatsIntegrator
    QUANTSTATS_AVAILABLE = True
except ImportError:
    QUANTSTATS_AVAILABLE = False
    logger.warning("Quantstats integration not available")

try:
    from utils.output_manager import get_output_manager
    OUTPUT_MANAGER_AVAILABLE = True
except ImportError:
    OUTPUT_MANAGER_AVAILABLE = False
    logger.warning("Output manager not available")

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """表现指标计算器"""

    @staticmethod
    def calculate_returns(pnl_series: pd.Series, position_data: pd.DataFrame = None, strategy_name: str = None, parent_strategy: str = None, config=None) -> pd.Series:
        """计算收益率序列 - 增强版本支持策略-持仓映射配置

        Args:
            pnl_series: 日度PnL序列 (元)
            position_data: 持仓数据 (元)
            strategy_name: 策略名称，用于匹配持仓数据
            parent_strategy: 父策略名称，用于层级分析时的持仓基准
            config: 配置对象，包含策略-持仓映射规则

        Returns:
            收益率序列 (百分比)
        """
        if position_data is not None and strategy_name is not None:
            # 使用实际持仓数据计算收益率
            position_data = position_data.copy()
            position_data['date'] = pd.to_datetime(position_data['date'])

            # 确定用于计算收益率的持仓基准
            # 策略类别级别：使用自身持仓
            # 其他层级：使用父策略类别持仓
            base_strategy = parent_strategy if parent_strategy else strategy_name

            # 获取策略映射规则
            if config and hasattr(config, 'analysis') and hasattr(config.analysis, 'strategy_position_mapping'):
                mapping_config = config.analysis.strategy_position_mapping

                # 处理配置结构（可能是对象或字典）
                if hasattr(mapping_config, 'mapping_rules'):
                    mapping_rules = mapping_config.mapping_rules
                    if hasattr(mapping_rules, 'strategy_level'):
                        strategy_mapping = mapping_rules.strategy_level
                    elif isinstance(mapping_rules, dict):
                        strategy_mapping = mapping_rules.get('strategy_level', {})
                    else:
                        strategy_mapping = {}
                elif isinstance(mapping_config, dict):
                    strategy_mapping = mapping_config.get('mapping_rules', {}).get('strategy_level', {})
                else:
                    strategy_mapping = {}

                # 获取默认配置
                if hasattr(mapping_config, 'default_position'):
                    default_pos = mapping_config.default_position
                    if hasattr(default_pos, 'fallback_strategy'):
                        fallback_strategy = default_pos.fallback_strategy
                        base_capital = getattr(default_pos, 'base_capital', 1000000)
                    elif isinstance(default_pos, dict):
                        fallback_strategy = default_pos.get('fallback_strategy', 'other')
                        base_capital = default_pos.get('base_capital', 1000000)
                    else:
                        fallback_strategy = 'other'
                        base_capital = 1000000
                elif isinstance(mapping_config, dict):
                    default_pos = mapping_config.get('default_position', {})
                    fallback_strategy = default_pos.get('fallback_strategy', 'other')
                    base_capital = default_pos.get('base_capital', 1000000)
                else:
                    fallback_strategy = 'other'
                    base_capital = 1000000
            else:
                # 使用默认映射规则
                strategy_mapping = {
                    'other': 'other',
                    'option': 'option',
                    'trend': 'trend',
                    'orderflow': 'other'
                }
                fallback_strategy = 'other'
                base_capital = 1000000

            # 检查数据格式：长格式还是宽格式
            if 'strategy_category' in position_data.columns and 'position' in position_data.columns:
                # 长格式数据：已经转换过的格式
                # 处理NaN策略名称
                if pd.isna(base_strategy) or base_strategy is None:
                    mapped_strategy = fallback_strategy
                else:
                    mapped_strategy = strategy_mapping.get(str(base_strategy), fallback_strategy)

                # 筛选对应策略的持仓数据
                strategy_position_data = position_data[position_data['strategy_category'] == mapped_strategy]
                if strategy_position_data.empty:
                    # 如果没有找到对应策略，使用备用策略
                    strategy_position_data = position_data[position_data['strategy_category'] == fallback_strategy]

                # 创建日期对齐的持仓序列
                if not strategy_position_data.empty:
                    position_series = strategy_position_data.set_index('date')['position']
                else:
                    position_series = pd.Series(base_capital, index=pnl_series.index)

            else:
                # 宽格式数据：原始格式
                # 处理NaN策略名称
                if pd.isna(strategy_name) or strategy_name is None:
                    position_col = fallback_strategy
                else:
                    position_col = strategy_mapping.get(str(strategy_name), fallback_strategy)

                if position_col not in position_data.columns:
                    position_col = fallback_strategy

                # 创建日期对齐的持仓序列
                if position_col in position_data.columns:
                    position_series = position_data.set_index('date')[position_col]
                else:
                    position_series = pd.Series(base_capital, index=pnl_series.index)

            # 对齐PnL和持仓数据
            aligned_data = pd.DataFrame({
                'pnl': pnl_series,
                'position': position_series
            }).ffill()  # 前向填充持仓数据

            # 计算收益率 = PnL / 持仓金额
            returns = aligned_data['pnl'] / aligned_data['position']
            returns = returns.replace([np.inf, -np.inf], 0).fillna(0)

            # 数据验证
            returns = PerformanceMetrics._validate_returns(returns, config)

            return returns
        else:
            # 回退到原有方法（用于向后兼容）
            base_capital = 1000000
            if config and hasattr(config, 'analysis') and hasattr(config.analysis, 'strategy_position_mapping'):
                # 安全获取base_capital
                try:
                    if hasattr(config.analysis.strategy_position_mapping.default_position, 'base_capital'):
                        base_capital = config.analysis.strategy_position_mapping.default_position.base_capital
                    elif isinstance(config.analysis.strategy_position_mapping.default_position, dict):
                        base_capital = config.analysis.strategy_position_mapping.default_position.get('base_capital', 1000000)
                    else:
                        base_capital = 1000000
                except:
                    base_capital = 1000000

            cumulative_pnl = pnl_series.cumsum()
            returns = pnl_series / (base_capital + cumulative_pnl.shift(1).fillna(0))
            return PerformanceMetrics._validate_returns(returns.fillna(0), config)

    @staticmethod
    def _validate_returns(returns: pd.Series, config=None) -> pd.Series:
        """验证收益率数据"""
        if config and hasattr(config, 'analysis') and hasattr(config.analysis, 'risk_metrics') and hasattr(config.analysis.risk_metrics, 'validation'):
            validation_config = config.analysis.risk_metrics.validation

            # 检查异常值
            if validation_config.enable_outlier_detection:
                threshold = validation_config.outlier_threshold
                mean_return = returns.mean()
                std_return = returns.std()

                if std_return > 0:  # 避免除零错误
                    # 标记异常值
                    outliers = np.abs(returns - mean_return) > threshold * std_return
                    if outliers.any():
                        logger.warning(f"Found {outliers.sum()} outlier returns, capping them")
                        # 限制异常值
                        upper_bound = mean_return + threshold * std_return
                        lower_bound = mean_return - threshold * std_return
                        returns = returns.clip(lower_bound, upper_bound)

        return returns
    
    @staticmethod
    def calculate_cumulative_returns(returns: pd.Series) -> pd.Series:
        """计算累积收益率"""
        return (1 + returns).cumprod() - 1
    
    @staticmethod
    def calculate_sharpe_ratio(returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """计算夏普比率"""
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate / 252  # 日度无风险利率
        return np.sqrt(252) * excess_returns.mean() / returns.std()
    
    @staticmethod
    def calculate_sortino_ratio(returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """计算索提诺比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = returns - risk_free_rate / 252
        downside_returns = returns[returns < 0]
        
        if len(downside_returns) == 0 or downside_returns.std() == 0:
            return float('inf') if excess_returns.mean() > 0 else 0.0
        
        return np.sqrt(252) * excess_returns.mean() / downside_returns.std()
    
    @staticmethod
    def calculate_max_drawdown(cumulative_returns: pd.Series) -> Dict[str, Any]:
        """计算最大回撤"""
        if len(cumulative_returns) == 0:
            return {"max_drawdown": 0.0, "start_date": None, "end_date": None, "recovery_date": None}
        
        # 计算累积最高点
        peak = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - peak) / (1 + peak)
        
        max_dd = drawdown.min()
        max_dd_idx = drawdown.idxmin()
        
        # 找到回撤开始点
        start_idx = peak[:max_dd_idx].idxmax()
        
        # 找到恢复点
        recovery_idx = None
        peak_value = peak.loc[start_idx]
        for idx in cumulative_returns[max_dd_idx:].index:
            if cumulative_returns.loc[idx] >= peak_value:
                recovery_idx = idx
                break
        
        return {
            "max_drawdown": abs(max_dd),
            "start_date": start_idx,
            "end_date": max_dd_idx,
            "recovery_date": recovery_idx,
            "drawdown_series": drawdown
        }
    
    @staticmethod
    def calculate_var_es(returns: pd.Series, confidence_levels: List[float] = [0.95, 0.99]) -> Dict[str, Dict[str, float]]:
        """计算VaR和ES"""
        if len(returns) == 0:
            return {f"{int(cl*100)}%": {"VaR": 0.0, "ES": 0.0} for cl in confidence_levels}
        
        results = {}
        for cl in confidence_levels:
            # VaR计算
            var = np.percentile(returns, (1 - cl) * 100)
            
            # ES计算（超过VaR的平均损失）
            tail_returns = returns[returns <= var]
            es = tail_returns.mean() if len(tail_returns) > 0 else var
            
            results[f"{int(cl*100)}%"] = {
                "VaR": abs(var),
                "ES": abs(es)
            }
        
        return results
    
    @staticmethod
    def calculate_rolling_metrics(returns: pd.Series, window: int = 30) -> Dict[str, pd.Series]:
        """计算滚动指标"""
        rolling_return = returns.rolling(window).mean() * 252  # 年化收益率
        rolling_vol = returns.rolling(window).std() * np.sqrt(252)  # 年化波动率
        rolling_sharpe = rolling_return / rolling_vol
        
        return {
            "rolling_return": rolling_return,
            "rolling_volatility": rolling_vol,
            "rolling_sharpe": rolling_sharpe
        }


class StrategyPerformanceAnalyzer:
    """策略表现分析器 - Enhanced with Quantstats Integration"""

    def __init__(self, config: CTAConfig):
        self.config = config
        self.metrics = PerformanceMetrics()
        self.position_data = None  # 将在analyze_performance中设置
        self.time_calculator = TimeWindowCalculator(config)  # 时间窗口计算器

        # Initialize quantstats integrator if available
        if QUANTSTATS_AVAILABLE:
            self.quantstats = QuantstatsIntegrator(config)
            logger.info("Quantstats integration enabled")
        else:
            self.quantstats = None
            logger.warning("Quantstats integration disabled")
    
    def analyze_strategy_category_performance(self, cta_data: pd.DataFrame, position_data: pd.DataFrame = None) -> Dict[str, Dict[str, Any]]:
        """分析策略类别表现"""
        logger.info("Analyzing strategy category performance...")

        # 存储持仓数据供其他方法使用
        self.position_data = position_data

        results = {}
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column

        # 过滤掉NaN策略
        valid_strategies = cta_data[strategy_col].dropna().unique()

        for strategy in valid_strategies:
            strategy_data = cta_data[cta_data[strategy_col] == strategy]

            # 按日期聚合PnL
            daily_pnl = strategy_data.groupby(date_col)[pnl_col].sum()
            returns = self.metrics.calculate_returns(daily_pnl, position_data, strategy, None, self.config)
            cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
            
            # 计算各项指标 - Enhanced with Quantstats
            strategy_metrics = {
                "total_pnl": daily_pnl.sum(),
                "daily_pnl": daily_pnl,
                "returns": returns,
                "cumulative_returns": cumulative_returns,
                "annual_return": returns.mean() * 252,
                "annual_volatility": returns.std() * np.sqrt(252),
                "win_rate": (daily_pnl > 0).mean(),
                "avg_win": daily_pnl[daily_pnl > 0].mean() if (daily_pnl > 0).any() else 0,
                "avg_loss": daily_pnl[daily_pnl < 0].mean() if (daily_pnl < 0).any() else 0,
                "profit_factor": abs(daily_pnl[daily_pnl > 0].sum() / daily_pnl[daily_pnl < 0].sum()) if (daily_pnl < 0).any() else float('inf')
            }

            # Use quantstats for enhanced metrics if available
            if self.quantstats and len(returns) > 0:
                try:
                    qs_metrics = self.quantstats.calculate_performance_metrics(returns)
                    strategy_metrics.update({
                        "quantstats_metrics": qs_metrics,
                        "sharpe_ratio": qs_metrics.get("sharpe_ratio", self.metrics.calculate_sharpe_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate)),
                        "sortino_ratio": qs_metrics.get("sortino_ratio", self.metrics.calculate_sortino_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate)),
                        "max_drawdown": qs_metrics.get("max_drawdown", self.metrics.calculate_max_drawdown(cumulative_returns)),
                        "calmar_ratio": qs_metrics.get("calmar_ratio", 0),
                        "var_95": qs_metrics.get("var_95", 0),
                        "cvar_95": qs_metrics.get("cvar_95", 0),
                        "tail_ratio": qs_metrics.get("tail_ratio", 0),
                        "ulcer_index": qs_metrics.get("ulcer_index", 0)
                    })
                except Exception as e:
                    logger.warning(f"Quantstats metrics calculation failed for {strategy}: {e}")
                    # Fallback to original metrics
                    strategy_metrics.update({
                        "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate),
                        "sortino_ratio": self.metrics.calculate_sortino_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate),
                        "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                        "var_es": self.metrics.calculate_var_es(returns, self.config.analysis.risk_metrics.var_confidence_levels),
                        "rolling_metrics": self.metrics.calculate_rolling_metrics(returns)
                    })
            else:
                # Fallback to original metrics
                strategy_metrics.update({
                    "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate),
                    "sortino_ratio": self.metrics.calculate_sortino_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate),
                    "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                    "var_es": self.metrics.calculate_var_es(returns, self.config.analysis.risk_metrics.var_confidence_levels),
                    "rolling_metrics": self.metrics.calculate_rolling_metrics(returns)
                })

            results[strategy] = strategy_metrics
        
        return results
    
    def analyze_trend_strategy_details(self, cta_data: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """分析trend策略的详细表现（按signal和frequency）"""
        logger.info("Analyzing trend strategy details...")
        
        trend_data = cta_data[cta_data[self.config.data.strategy_category_column] == 'trend']
        if trend_data.empty:
            return {}
        
        results = {
            "by_signal": {},
            "by_frequency": {},
            "by_signal_frequency": {}
        }
        
        signal_col = self.config.data.strategy_signal_column
        freq_col = self.config.data.signal_freq_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column
        
        # 按信号分析
        if signal_col in trend_data.columns:
            for signal in trend_data[signal_col].unique():
                signal_data = trend_data[trend_data[signal_col] == signal]
                daily_pnl = signal_data.groupby(date_col)[pnl_col].sum()
                returns = self.metrics.calculate_returns(daily_pnl, self.position_data, 'trend', 'trend', self.config)
                cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                
                results["by_signal"][signal] = {
                    "total_pnl": daily_pnl.sum(),
                    "daily_pnl": daily_pnl,
                    "returns": returns,
                    "cumulative_returns": cumulative_returns,
                    "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns),
                    "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                    "annual_return": returns.mean() * 252,
                    "annual_volatility": returns.std() * np.sqrt(252)
                }
        
        # 按频率分析
        if freq_col in trend_data.columns:
            for freq in trend_data[freq_col].unique():
                freq_data = trend_data[trend_data[freq_col] == freq]
                daily_pnl = freq_data.groupby(date_col)[pnl_col].sum()
                returns = self.metrics.calculate_returns(daily_pnl, self.position_data, 'trend', 'trend')
                cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                
                results["by_frequency"][freq] = {
                    "total_pnl": daily_pnl.sum(),
                    "daily_pnl": daily_pnl,
                    "returns": returns,
                    "cumulative_returns": cumulative_returns,
                    "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns),
                    "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                    "annual_return": returns.mean() * 252,
                    "annual_volatility": returns.std() * np.sqrt(252)
                }
        
        # 按信号和频率组合分析
        if signal_col in trend_data.columns and freq_col in trend_data.columns:
            for signal in trend_data[signal_col].unique():
                results["by_signal_frequency"][signal] = {}
                signal_data = trend_data[trend_data[signal_col] == signal]
                
                for freq in signal_data[freq_col].unique():
                    combo_data = signal_data[signal_data[freq_col] == freq]
                    daily_pnl = combo_data.groupby(date_col)[pnl_col].sum()
                    returns = self.metrics.calculate_returns(daily_pnl, self.position_data, 'trend', 'trend')
                    cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                    
                    results["by_signal_frequency"][signal][freq] = {
                        "total_pnl": daily_pnl.sum(),
                        "daily_pnl": daily_pnl,
                        "returns": returns,
                        "cumulative_returns": cumulative_returns,
                        "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns),
                        "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                        "annual_return": returns.mean() * 252,
                        "annual_volatility": returns.std() * np.sqrt(252)
                    }
        
        return results
    
    def analyze_industry_performance(self, cta_data: pd.DataFrame, by_strategy: bool = True) -> Dict[str, Dict[str, Any]]:
        """分析行业表现"""
        logger.info("Analyzing industry performance...")
        
        if self.config.data.industry_column not in cta_data.columns:
            logger.warning("Industry column not found in data")
            return {}
        
        results = {}
        industry_col = self.config.data.industry_column
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column
        
        if by_strategy:
            # 按策略类别分析各行业表现
            for strategy in cta_data[strategy_col].unique():
                results[strategy] = {}
                strategy_data = cta_data[cta_data[strategy_col] == strategy]
                
                for industry in strategy_data[industry_col].unique():
                    industry_data = strategy_data[strategy_data[industry_col] == industry]
                    daily_pnl = industry_data.groupby(date_col)[pnl_col].sum()
                    returns = self.metrics.calculate_returns(daily_pnl, self.position_data, industry, strategy)
                    cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                    
                    results[strategy][industry] = {
                        "total_pnl": daily_pnl.sum(),
                        "daily_pnl": daily_pnl,
                        "returns": returns,
                        "cumulative_returns": cumulative_returns,
                        "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns),
                        "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                        "annual_return": returns.mean() * 252,
                        "annual_volatility": returns.std() * np.sqrt(252)
                    }
        else:
            # 整体行业分析
            for industry in cta_data[industry_col].unique():
                industry_data = cta_data[cta_data[industry_col] == industry]
                daily_pnl = industry_data.groupby(date_col)[pnl_col].sum()
                returns = self.metrics.calculate_returns(daily_pnl, self.position_data, industry, None)
                cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                
                results[industry] = {
                    "total_pnl": daily_pnl.sum(),
                    "daily_pnl": daily_pnl,
                    "returns": returns,
                    "cumulative_returns": cumulative_returns,
                    "sharpe_ratio": self.metrics.calculate_sharpe_ratio(returns),
                    "max_drawdown": self.metrics.calculate_max_drawdown(cumulative_returns),
                    "annual_return": returns.mean() * 252,
                    "annual_volatility": returns.std() * np.sqrt(252)
                }
        
        return results

    def analyze_symbol_category_performance(self, cta_data: pd.DataFrame,
                                          time_windows: List[str] = None) -> Dict[str, Dict[str, Any]]:
        """分析品种类别表现"""
        logger.info("Analyzing symbol category performance...")

        if self.config.data.symbol_category_column not in cta_data.columns:
            logger.warning("Symbol category column not found in data")
            return {}

        if time_windows is None:
            time_windows = ["daily", "weekly", "monthly"]

        results = {}
        symbol_col = self.config.data.symbol_category_column
        strategy_col = self.config.data.strategy_category_column
        freq_col = self.config.data.signal_freq_column
        industry_col = self.config.data.industry_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column

        # 获取最新日期
        latest_date = cta_data[date_col].max()

        for window_name in time_windows:
            # 使用时间窗口计算器获取正确的日历边界
            try:
                start_date, end_date = self.time_calculator.calculate_time_window_boundaries(latest_date, window_name)
            except Exception as e:
                logger.warning(f"Failed to calculate time window for {window_name}: {e}")
                continue

            window_data = cta_data[(cta_data[date_col] >= start_date) & (cta_data[date_col] <= end_date)]

            if window_data.empty:
                continue

            # 整体品种表现
            symbol_performance = self._calculate_symbol_performance(window_data, symbol_col)

            # 按策略分组的品种表现
            strategy_symbol_performance = {}
            for strategy in window_data[strategy_col].unique():
                strategy_data = window_data[window_data[strategy_col] == strategy]
                strategy_symbol_performance[strategy] = self._calculate_symbol_performance(
                    strategy_data, symbol_col
                )

            # 按信号频率分组的品种表现
            freq_symbol_performance = {}
            if freq_col in window_data.columns:
                for freq in window_data[freq_col].unique():
                    freq_data = window_data[window_data[freq_col] == freq]
                    freq_symbol_performance[freq] = self._calculate_symbol_performance(
                        freq_data, symbol_col
                    )

            # 按行业分组的品种表现
            industry_symbol_performance = {}
            if industry_col in window_data.columns:
                for industry in window_data[industry_col].unique():
                    industry_data = window_data[window_data[industry_col] == industry]
                    industry_symbol_performance[industry] = self._calculate_symbol_performance(
                        industry_data, symbol_col
                    )

            results[window_name] = {
                "overall": symbol_performance,
                "by_strategy": strategy_symbol_performance,
                "by_frequency": freq_symbol_performance,
                "by_industry": industry_symbol_performance,
                "start_date": start_date,
                "end_date": latest_date
            }

        return results

    def _calculate_symbol_performance(self, data: pd.DataFrame, symbol_col: str) -> Dict[str, Dict[str, Any]]:
        """计算品种表现指标"""
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column

        symbol_performance = {}

        for symbol in data[symbol_col].unique():
            symbol_data = data[data[symbol_col] == symbol]

            # 按日期聚合PnL
            daily_pnl = symbol_data.groupby(date_col)[pnl_col].sum()

            if len(daily_pnl) == 0:
                continue

            # 计算收益率 - 品种级别使用整体持仓作为基准
            returns = self.metrics.calculate_returns(daily_pnl, self.position_data, symbol, None)

            # 计算指标
            total_pnl = daily_pnl.sum()
            trading_days = len(daily_pnl)
            avg_daily_pnl = daily_pnl.mean()

            # 计算收益率相关指标
            if len(returns) > 1:
                annual_return = returns.mean() * 252
                annual_volatility = returns.std() * np.sqrt(252)
                sharpe_ratio = self.metrics.calculate_sharpe_ratio(returns, self.config.analysis.risk_metrics.risk_free_rate)

                # 计算最大回撤
                cumulative_returns = self.metrics.calculate_cumulative_returns(returns)
                max_drawdown_info = self.metrics.calculate_max_drawdown(cumulative_returns)
                max_drawdown = max_drawdown_info["max_drawdown"]
            else:
                annual_return = 0
                annual_volatility = 0
                sharpe_ratio = 0
                max_drawdown = 0

            # 胜率
            win_rate = (daily_pnl > 0).mean() if len(daily_pnl) > 0 else 0

            symbol_performance[symbol] = {
                "total_pnl": total_pnl,
                "trading_days": trading_days,
                "avg_daily_pnl": avg_daily_pnl,
                "annual_return": annual_return,
                "annual_volatility": annual_volatility,
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "win_rate": win_rate,
                "daily_pnl": daily_pnl,
                "returns": returns
            }

        return symbol_performance

    def get_top_symbol_categories(self, symbol_performance: Dict[str, Dict[str, Any]],
                                 top_n: int = 5, sort_by: str = "total_pnl") -> Dict[str, List[Dict[str, Any]]]:
        """获取表现最好和最差的品种"""
        if not symbol_performance:
            return {"top_performers": [], "worst_performers": []}

        # 转换为列表格式
        symbol_list = []
        for symbol, metrics in symbol_performance.items():
            symbol_info = {"symbol": symbol}
            symbol_info.update(metrics)
            symbol_list.append(symbol_info)

        # 按指定指标排序
        sorted_symbols = sorted(symbol_list, key=lambda x: x.get(sort_by, 0), reverse=True)

        # 获取前N名和后N名
        top_performers = sorted_symbols[:top_n]
        worst_performers = sorted_symbols[-top_n:] if len(sorted_symbols) > top_n else []

        return {
            "top_performers": top_performers,
            "worst_performers": worst_performers
        }
    
    def calculate_time_window_performance(self, cta_data: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """计算不同时间窗口的表现 - 使用日历期间"""
        logger.info("Calculating time window performance with calendar periods...")

        results = {}
        date_col = self.config.data.date_column
        pnl_col = self.config.data.pnl_column

        # 获取最新日期
        latest_date = cta_data[date_col].max()

        # 获取所有时间窗口信息
        time_windows_info = self.time_calculator.get_all_time_windows(latest_date)

        logger.info(f"Time window calculation summary:\n{self.time_calculator.format_time_window_summary(latest_date)}")

        for window_name, window_info in time_windows_info.items():
            start_date = window_info['start_date']
            end_date = window_info['end_date']

            # 过滤时间窗口数据
            window_data = cta_data[(cta_data[date_col] >= start_date) & (cta_data[date_col] <= end_date)]

            # 验证时间窗口数据
            validation = self.time_calculator.validate_time_window_data(
                cta_data, date_col, start_date, end_date
            )

            if not validation['valid']:
                logger.warning(f"Invalid data for {window_name}: {validation['error']}")
                continue
            
            if window_data.empty:
                continue
            
            # 整体表现
            daily_pnl = window_data.groupby(date_col)[pnl_col].sum()
            total_pnl = daily_pnl.sum()
            
            # 按策略类别表现
            strategy_performance = {}
            strategy_col = self.config.data.strategy_category_column
            
            for strategy in window_data[strategy_col].unique():
                strategy_data = window_data[window_data[strategy_col] == strategy]
                strategy_pnl = strategy_data.groupby(date_col)[pnl_col].sum().sum()
                strategy_performance[strategy] = strategy_pnl
            
            results[window_name] = {
                "total_pnl": total_pnl,
                "daily_pnl": daily_pnl,
                "strategy_performance": strategy_performance,
                "start_date": start_date,
                "end_date": end_date,
                "trading_days": len(daily_pnl),
                "description": window_info['description'],
                "calculation_method": self.time_calculator._get_calculation_method(),
                "data_validation": validation
            }
        
        return results

    def generate_quantstats_reports(self, performance_results: Dict[str, Dict[str, Any]],
                                  output_dir: str = None) -> Dict[str, str]:
        """
        Generate quantstats HTML reports and plots for each strategy

        Args:
            performance_results: Results from analyze_strategy_category_performance
            output_dir: Custom output directory (optional, uses output manager if None)

        Returns:
            Dictionary of generated report file paths
        """
        if not self.quantstats:
            logger.warning("Quantstats not available for report generation")
            return {}

        logger.info("正在生成quantstats报告和图表...")

        # Use output manager for standardized paths
        if OUTPUT_MANAGER_AVAILABLE and output_dir is None:
            output_manager = get_output_manager()
            base_output_dir = output_manager.get_output_path('charts', 'quantstats')
        else:
            import os
            base_output_dir = output_dir or "output/quantstats_reports"
            os.makedirs(base_output_dir, exist_ok=True)

        generated_files = {}

        for strategy, metrics in performance_results.items():
            if 'returns' not in metrics or len(metrics['returns']) == 0:
                continue

            try:
                # Create strategy-specific directory
                import os
                strategy_dir = os.path.join(base_output_dir, strategy)
                os.makedirs(strategy_dir, exist_ok=True)

                returns = metrics['returns']

                # Generate HTML report with standardized naming
                if OUTPUT_MANAGER_AVAILABLE and output_dir is None:
                    output_manager = get_output_manager()
                    html_filename = output_manager.generate_filename(
                        'quantstats', 'report', 'html', custom_suffix=strategy
                    )
                    html_file = os.path.join(strategy_dir, html_filename)
                else:
                    html_file = os.path.join(strategy_dir, f"{strategy}_quantstats_report.html")

                html_path = self.quantstats.generate_html_report(
                    returns,
                    output_file=html_file,
                    title=f"CTA策略表现报告 - {strategy}"
                )

                if html_path:
                    generated_files[f"{strategy}_html"] = html_path

                # Generate plots with standardized structure
                plots_dir = os.path.join(strategy_dir, "plots")
                plot_files = self.quantstats.generate_performance_plots(returns, output_dir=plots_dir)

                for plot_name, plot_path in plot_files.items():
                    generated_files[f"{strategy}_{plot_name}"] = plot_path

                logger.info(f"已生成策略quantstats报告: {strategy}")

            except Exception as e:
                logger.error(f"生成策略quantstats报告失败 {strategy}: {e}")

        logger.info(f"已生成 {len(generated_files)} 个quantstats文件")
        return generated_files
