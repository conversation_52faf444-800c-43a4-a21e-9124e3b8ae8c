#!/usr/bin/env python3
"""
Multi-Timeframe Reporting System

This module provides comprehensive multi-timeframe reporting capabilities
with separate configurations for each time dimension and command-line support.
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import argparse
import os
from pathlib import Path

# Import configuration and analysis modules
import sys
from pathlib import Path

# Add parent directory to path for imports
current_dir = Path(__file__).parent
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

from config.modular_config_manager import ModularConfigManager
from analysis.comprehensive_analyzer import ComprehensiveAnalyzer
from utils.time_window_calculator import TimeWindowCalculator

logger = logging.getLogger(__name__)


class MultiTimeframeReporter:
    """
    Multi-Timeframe Reporting System
    
    Generates separate reports for different time dimensions with
    customizable content, formats, and detail levels.
    """
    
    def __init__(self, config_manager: ModularConfigManager = None):
        """
        Initialize the multi-timeframe reporter
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager or ModularConfigManager()
        if not self.config_manager.is_initialized:
            self.config_manager.initialize()
        
        # Load reporting configuration
        self.reporting_config = self.config_manager.get_module_config('reporting')
        self.master_config = self.config_manager.get_master_config()
        
        # Initialize time calculator
        self.time_calculator = TimeWindowCalculator(self.master_config)
        
        # Initialize comprehensive analyzer
        self.analyzer = ComprehensiveAnalyzer(self.master_config)
        
        # Supported timeframes
        self.supported_timeframes = ['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom']
        
        # Report generation status
        self.generation_status = {}
    
    def generate_timeframe_report(self, timeframe: str, 
                                cta_data: pd.DataFrame,
                                position_data: pd.DataFrame,
                                custom_start_date: Optional[str] = None,
                                custom_end_date: Optional[str] = None,
                                output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate report for a specific timeframe
        
        Args:
            timeframe: Time dimension ('daily', 'weekly', 'monthly', etc.)
            cta_data: CTA strategy data
            position_data: Position data
            custom_start_date: Start date for custom timeframe (YYYY-MM-DD)
            custom_end_date: End date for custom timeframe (YYYY-MM-DD)
            output_dir: Custom output directory
            
        Returns:
            Report generation results
        """
        logger.info(f"Generating {timeframe} report...")
        
        try:
            # Validate timeframe
            if timeframe not in self.supported_timeframes:
                raise ValueError(f"Unsupported timeframe: {timeframe}")
            
            # Get timeframe configuration
            timeframe_config = self._get_timeframe_config(timeframe)
            if not timeframe_config:
                raise ValueError(f"No configuration found for timeframe: {timeframe}")
            
            # Calculate time window
            time_window = self._calculate_time_window(timeframe, custom_start_date, custom_end_date)
            
            # Filter data for time window
            filtered_data = self._filter_data_for_timeframe(
                cta_data, position_data, time_window
            )
            
            # Run analysis based on included modules
            analysis_results = self._run_timeframe_analysis(
                filtered_data['cta_data'], 
                filtered_data['position_data'],
                timeframe_config
            )
            
            # Generate reports in specified formats
            report_files = self._generate_timeframe_reports(
                analysis_results, timeframe_config, timeframe, time_window, output_dir
            )
            
            # Update generation status
            self.generation_status[timeframe] = {
                'status': 'success',
                'timestamp': datetime.now().isoformat(),
                'time_window': time_window,
                'files_generated': report_files,
                'data_records': len(filtered_data['cta_data'])
            }
            
            logger.info(f"Successfully generated {timeframe} report with {len(report_files)} files")
            return self.generation_status[timeframe]
            
        except Exception as e:
            logger.error(f"Failed to generate {timeframe} report: {e}")
            self.generation_status[timeframe] = {
                'status': 'failed',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
            return self.generation_status[timeframe]
    
    def generate_all_timeframe_reports(self, cta_data: pd.DataFrame,
                                     position_data: pd.DataFrame,
                                     enabled_timeframes: Optional[List[str]] = None,
                                     output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate reports for all enabled timeframes
        
        Args:
            cta_data: CTA strategy data
            position_data: Position data
            enabled_timeframes: List of timeframes to generate (None = all enabled)
            output_dir: Custom output directory
            
        Returns:
            Generation results for all timeframes
        """
        logger.info("Generating reports for all timeframes...")
        
        # Determine which timeframes to generate
        if enabled_timeframes is None:
            enabled_timeframes = self._get_enabled_timeframes()
        
        results = {}
        
        for timeframe in enabled_timeframes:
            try:
                result = self.generate_timeframe_report(
                    timeframe, cta_data, position_data, output_dir=output_dir
                )
                results[timeframe] = result
                
            except Exception as e:
                logger.error(f"Failed to generate {timeframe} report: {e}")
                results[timeframe] = {
                    'status': 'failed',
                    'error': str(e)
                }
        
        # Generate summary
        successful = sum(1 for r in results.values() if r.get('status') == 'success')
        total = len(results)
        
        logger.info(f"Completed timeframe report generation: {successful}/{total} successful")
        
        return {
            'summary': {
                'total_timeframes': total,
                'successful': successful,
                'failed': total - successful,
                'generation_time': datetime.now().isoformat()
            },
            'results': results
        }
    
    def _get_timeframe_config(self, timeframe: str) -> Optional[Dict[str, Any]]:
        """Get configuration for specific timeframe"""
        if not self.reporting_config:
            return None
        
        timeframe_configs = self.reporting_config.get('timeframe_reporting', {}).get('timeframes', {})
        return timeframe_configs.get(timeframe, {})
    
    def _get_enabled_timeframes(self) -> List[str]:
        """Get list of enabled timeframes"""
        enabled = []
        
        if not self.reporting_config:
            return ['daily', 'weekly', 'monthly']  # Default fallback
        
        timeframe_configs = self.reporting_config.get('timeframe_reporting', {}).get('timeframes', {})
        
        for timeframe, config in timeframe_configs.items():
            if config.get('enabled', False):
                enabled.append(timeframe)
        
        return enabled
    
    def _calculate_time_window(self, timeframe: str, 
                             custom_start_date: Optional[str] = None,
                             custom_end_date: Optional[str] = None) -> Dict[str, Any]:
        """Calculate time window for timeframe"""
        if timeframe == 'custom':
            if not custom_start_date or not custom_end_date:
                raise ValueError("Custom timeframe requires start_date and end_date")
            
            return {
                'start_date': pd.to_datetime(custom_start_date),
                'end_date': pd.to_datetime(custom_end_date),
                'timeframe': 'custom',
                'description': f"Custom period from {custom_start_date} to {custom_end_date}"
            }
        
        # Use time calculator for standard timeframes
        latest_date = datetime.now().date()
        time_windows = self.time_calculator.get_all_time_windows(latest_date)
        
        # Map timeframe names
        timeframe_mapping = {
            'daily': 'today',
            'weekly': 'this_week',
            'monthly': 'this_month',
            'quarterly': 'this_quarter',
            'yearly': 'this_year'
        }
        
        window_key = timeframe_mapping.get(timeframe, timeframe)
        
        if window_key in time_windows:
            return time_windows[window_key]
        else:
            raise ValueError(f"Time window not available for timeframe: {timeframe}")
    
    def _filter_data_for_timeframe(self, cta_data: pd.DataFrame, 
                                 position_data: pd.DataFrame,
                                 time_window: Dict[str, Any]) -> Dict[str, pd.DataFrame]:
        """Filter data for specific time window"""
        start_date = time_window['start_date']
        end_date = time_window['end_date']
        
        # Get date columns from configuration
        data_config = self.master_config.get('data', {})
        cta_date_col = data_config.get('column_mappings', {}).get('cta_data', {}).get('date_column', 'trade_date')
        pos_date_col = data_config.get('column_mappings', {}).get('position_data', {}).get('date_column', 'date')
        
        # Filter CTA data
        cta_data[cta_date_col] = pd.to_datetime(cta_data[cta_date_col])
        filtered_cta = cta_data[
            (cta_data[cta_date_col] >= start_date) & 
            (cta_data[cta_date_col] <= end_date)
        ].copy()
        
        # Filter position data
        position_data[pos_date_col] = pd.to_datetime(position_data[pos_date_col])
        filtered_position = position_data[
            (position_data[pos_date_col] >= start_date) & 
            (position_data[pos_date_col] <= end_date)
        ].copy()
        
        logger.info(f"Filtered data: CTA {len(filtered_cta)} records, Position {len(filtered_position)} records")
        
        return {
            'cta_data': filtered_cta,
            'position_data': filtered_position
        }
    
    def _run_timeframe_analysis(self, cta_data: pd.DataFrame,
                              position_data: pd.DataFrame,
                              timeframe_config: Dict[str, Any]) -> Dict[str, Any]:
        """Run analysis for timeframe based on included modules"""
        included_modules = timeframe_config.get('included_modules', [])
        
        logger.info(f"Running analysis with modules: {included_modules}")
        
        # Run comprehensive analysis
        analysis_results = self.analyzer.run_comprehensive_analysis(cta_data, position_data)
        
        # Filter results based on included modules
        filtered_results = {}
        
        for module in included_modules:
            if module in analysis_results:
                filtered_results[module] = analysis_results[module]
            else:
                logger.warning(f"Module {module} not found in analysis results")
        
        # Add metadata
        filtered_results['metadata'] = {
            'included_modules': included_modules,
            'analysis_timestamp': datetime.now().isoformat(),
            'data_records': len(cta_data)
        }
        
        return filtered_results
    
    def _generate_timeframe_reports(self, analysis_results: Dict[str, Any],
                                  timeframe_config: Dict[str, Any],
                                  timeframe: str,
                                  time_window: Dict[str, Any],
                                  output_dir: Optional[str] = None) -> List[str]:
        """Generate reports in specified formats"""
        output_formats = timeframe_config.get('output_formats', ['excel'])
        chart_types = timeframe_config.get('chart_types', [])
        content_depth = timeframe_config.get('content_depth', 'summary')
        
        # Determine output directory
        if output_dir is None:
            base_dir = self.master_config.get('output', {}).get('base_directory', 'output')
            output_dir = os.path.join(base_dir, 'timeframe_reports', timeframe)
        
        os.makedirs(output_dir, exist_ok=True)
        
        generated_files = []
        
        # Generate timestamp for file naming
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Generate reports in each format
        for format_type in output_formats:
            try:
                if format_type == 'excel':
                    file_path = self._generate_excel_report(
                        analysis_results, timeframe, time_window, content_depth, 
                        output_dir, timestamp
                    )
                    generated_files.append(file_path)
                
                elif format_type == 'html':
                    file_path = self._generate_html_report(
                        analysis_results, timeframe, time_window, content_depth,
                        output_dir, timestamp
                    )
                    generated_files.append(file_path)
                
                elif format_type == 'markdown':
                    file_path = self._generate_markdown_report(
                        analysis_results, timeframe, time_window, content_depth,
                        output_dir, timestamp
                    )
                    generated_files.append(file_path)
                
                elif format_type == 'pdf':
                    file_path = self._generate_pdf_report(
                        analysis_results, timeframe, time_window, content_depth,
                        output_dir, timestamp
                    )
                    generated_files.append(file_path)
                
            except Exception as e:
                logger.error(f"Failed to generate {format_type} report: {e}")
        
        # Generate charts if specified
        if chart_types:
            chart_files = self._generate_timeframe_charts(
                analysis_results, chart_types, output_dir, timestamp
            )
            generated_files.extend(chart_files)
        
        return generated_files
    
    def _generate_excel_report(self, analysis_results: Dict[str, Any],
                             timeframe: str, time_window: Dict[str, Any],
                             content_depth: str, output_dir: str,
                             timestamp: str) -> str:
        """Generate Excel report for timeframe"""
        filename = f"{timeframe}_report_{timestamp}.xlsx"
        file_path = os.path.join(output_dir, filename)
        
        # Use existing Excel reporter with timeframe-specific configuration
        from .excel_reporter import ExcelReporter
        
        excel_reporter = ExcelReporter(self.master_config)
        
        # Generate Excel report
        excel_file = excel_reporter.generate_comprehensive_report(
            performance_results=analysis_results.get('performance_analysis', {}),
            risk_results=analysis_results.get('risk_analysis', {}),
            contribution_results=analysis_results.get('contribution_analysis', {}),
            output_file=file_path
        )
        
        logger.info(f"Generated Excel report: {file_path}")
        return file_path
    
    def _generate_html_report(self, analysis_results: Dict[str, Any],
                            timeframe: str, time_window: Dict[str, Any],
                            content_depth: str, output_dir: str,
                            timestamp: str) -> str:
        """Generate HTML report for timeframe"""
        filename = f"{timeframe}_report_{timestamp}.html"
        file_path = os.path.join(output_dir, filename)
        
        # Generate basic HTML report
        html_content = self._create_html_content(analysis_results, timeframe, time_window, content_depth)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Generated HTML report: {file_path}")
        return file_path
    
    def _generate_markdown_report(self, analysis_results: Dict[str, Any],
                                timeframe: str, time_window: Dict[str, Any],
                                content_depth: str, output_dir: str,
                                timestamp: str) -> str:
        """Generate Markdown report for timeframe"""
        filename = f"{timeframe}_report_{timestamp}.md"
        file_path = os.path.join(output_dir, filename)
        
        # Use existing Markdown reporter
        from .markdown_reporter import MarkdownReporter
        
        markdown_reporter = MarkdownReporter(self.master_config)
        
        # Generate Markdown report
        markdown_file = markdown_reporter.generate_comprehensive_report(
            analysis_results, output_file=file_path
        )
        
        logger.info(f"Generated Markdown report: {file_path}")
        return file_path
    
    def _generate_pdf_report(self, analysis_results: Dict[str, Any],
                           timeframe: str, time_window: Dict[str, Any],
                           content_depth: str, output_dir: str,
                           timestamp: str) -> str:
        """Generate PDF report for timeframe"""
        filename = f"{timeframe}_report_{timestamp}.pdf"
        file_path = os.path.join(output_dir, filename)
        
        # PDF generation would require additional libraries like reportlab
        # For now, create a placeholder
        logger.warning("PDF report generation not yet implemented")
        return ""
    
    def _generate_timeframe_charts(self, analysis_results: Dict[str, Any],
                                 chart_types: List[str], output_dir: str,
                                 timestamp: str) -> List[str]:
        """Generate charts for timeframe"""
        chart_files = []
        
        # Chart generation would use visualization modules
        # For now, return empty list
        logger.info(f"Chart generation requested for types: {chart_types}")
        
        return chart_files
    
    def _create_html_content(self, analysis_results: Dict[str, Any],
                           timeframe: str, time_window: Dict[str, Any],
                           content_depth: str) -> str:
        """Create HTML content for report"""
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>CTA Analysis - {timeframe.title()} Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; border: 1px solid #ddd; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>CTA Strategy Analysis - {timeframe.title()} Report</h1>
                <p>Time Period: {time_window.get('start_date', 'N/A')} to {time_window.get('end_date', 'N/A')}</p>
                <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Analysis Summary</h2>
                <p>Content depth: {content_depth}</p>
                <p>Modules included: {', '.join(analysis_results.get('metadata', {}).get('included_modules', []))}</p>
            </div>
            
            <div class="section">
                <h2>Key Metrics</h2>
                <!-- Metrics would be populated here -->
                <p>Detailed metrics and analysis results would be displayed here.</p>
            </div>
        </body>
        </html>
        """
        
        return html_template


def main():
    """Command-line interface for multi-timeframe reporting"""
    parser = argparse.ArgumentParser(description='CTA Multi-Timeframe Reporting System')

    # Timeframe selection
    parser.add_argument('--timeframe', '-t',
                       choices=['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom', 'all'],
                       default='all',
                       help='Timeframe for report generation')

    # Custom timeframe options
    parser.add_argument('--start-date', '-s',
                       help='Start date for custom timeframe (YYYY-MM-DD)')

    parser.add_argument('--end-date', '-e',
                       help='End date for custom timeframe (YYYY-MM-DD)')

    # Data file options
    parser.add_argument('--cta-data', '-c',
                       default='input/processed_cta_data.csv',
                       help='Path to CTA data file')

    parser.add_argument('--position-data', '-p',
                       default='input/strategy_position.csv',
                       help='Path to position data file')

    # Output options
    parser.add_argument('--output-dir', '-o',
                       help='Custom output directory')

    parser.add_argument('--formats', '-f',
                       nargs='+',
                       choices=['excel', 'html', 'markdown', 'pdf'],
                       help='Output formats to generate')

    # Configuration options
    parser.add_argument('--config',
                       default='config/master_config.yaml',
                       help='Path to configuration file')

    # Logging options
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')

    parser.add_argument('--quiet', '-q',
                       action='store_true',
                       help='Suppress non-error output')

    args = parser.parse_args()

    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.WARNING if args.quiet else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    try:
        # Initialize configuration manager
        config_manager = ModularConfigManager(args.config)
        if not config_manager.initialize():
            print("❌ Failed to initialize configuration manager")
            return 1

        # Initialize reporter
        reporter = MultiTimeframeReporter(config_manager)

        # Load data
        print(f"📊 Loading data from {args.cta_data} and {args.position_data}")

        try:
            cta_data = pd.read_csv(args.cta_data, encoding='utf-8-sig')
            position_data = pd.read_csv(args.position_data, encoding='utf-8-sig')
            print(f"✅ Data loaded: CTA {len(cta_data)} records, Position {len(position_data)} records")
        except Exception as e:
            print(f"❌ Failed to load data: {e}")
            return 1

        # Generate reports
        if args.timeframe == 'all':
            print("📈 Generating reports for all enabled timeframes...")
            results = reporter.generate_all_timeframe_reports(
                cta_data, position_data, output_dir=args.output_dir
            )

            # Print summary
            summary = results['summary']
            print(f"\n📋 Generation Summary:")
            print(f"  Total timeframes: {summary['total_timeframes']}")
            print(f"  Successful: {summary['successful']}")
            print(f"  Failed: {summary['failed']}")

            # Print individual results
            for timeframe, result in results['results'].items():
                status = "✅" if result.get('status') == 'success' else "❌"
                print(f"  {status} {timeframe}: {result.get('status', 'unknown')}")
                if result.get('files_generated'):
                    print(f"    Files: {len(result['files_generated'])}")

        else:
            print(f"📈 Generating {args.timeframe} report...")

            # Validate custom timeframe parameters
            if args.timeframe == 'custom':
                if not args.start_date or not args.end_date:
                    print("❌ Custom timeframe requires --start-date and --end-date")
                    return 1

            result = reporter.generate_timeframe_report(
                args.timeframe, cta_data, position_data,
                custom_start_date=args.start_date,
                custom_end_date=args.end_date,
                output_dir=args.output_dir
            )

            if result.get('status') == 'success':
                print(f"✅ {args.timeframe} report generated successfully")
                print(f"  Files generated: {len(result.get('files_generated', []))}")
                print(f"  Data records: {result.get('data_records', 0)}")

                # List generated files
                for file_path in result.get('files_generated', []):
                    print(f"    📄 {file_path}")
            else:
                print(f"❌ {args.timeframe} report generation failed: {result.get('error', 'Unknown error')}")
                return 1

        print("\n🎉 Multi-timeframe reporting completed!")
        return 0

    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
