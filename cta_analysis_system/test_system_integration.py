#!/usr/bin/env python3
"""
CTA策略分析系统集成测试

完整的端到端系统测试，验证所有模块和功能的正确性。
"""

import pandas as pd
import numpy as np
import logging
import os
import sys
from datetime import datetime
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('system_integration_test.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)


def test_output_manager():
    """测试输出管理器"""
    logger.info("🔧 测试输出管理器...")
    
    try:
        from utils.output_manager import OutputManager
        
        # 初始化输出管理器
        output_manager = OutputManager()
        
        # 测试目录创建
        chart_path = output_manager.get_output_path('charts', 'performance')
        report_path = output_manager.get_output_path('reports', 'excel')
        
        # 测试文件名生成
        filename = output_manager.generate_filename('test', 'report', 'xlsx')
        
        # 测试文件保存
        test_content = "测试内容"
        saved_file = output_manager.save_file(
            test_content, 'reports', 'markdown', 'test', 'integration', 'md'
        )
        
        logger.info(f"✅ 输出管理器测试通过")
        logger.info(f"  - 图表路径: {chart_path}")
        logger.info(f"  - 报告路径: {report_path}")
        logger.info(f"  - 生成文件名: {filename}")
        logger.info(f"  - 保存文件: {saved_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 输出管理器测试失败: {e}")
        return False


def test_configuration_system():
    """测试配置系统"""
    logger.info("🔧 测试配置系统...")
    
    try:
        from config.modular_config_manager import ModularConfigManager
        
        # 初始化配置管理器
        config_manager = ModularConfigManager()
        success = config_manager.initialize()
        
        if not success:
            logger.error("❌ 配置管理器初始化失败")
            return False
        
        # 测试模块配置加载
        pnl_config = config_manager.get_module_config('pnl_analysis')
        risk_config = config_manager.get_module_config('risk_analysis')
        
        # 测试配置验证
        validation_results = config_manager.validate_configuration()
        
        logger.info(f"✅ 配置系统测试通过")
        logger.info(f"  - PnL配置: {'已加载' if pnl_config else '未加载'}")
        logger.info(f"  - 风险配置: {'已加载' if risk_config else '未加载'}")
        logger.info(f"  - 配置验证: {'通过' if validation_results['is_valid'] else '失败'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置系统测试失败: {e}")
        return False


def test_data_loading():
    """测试数据加载"""
    logger.info("📊 测试数据加载...")
    
    try:
        # 检查数据文件是否存在
        cta_file = "input/processed_cta_data.csv"
        position_file = "input/strategy_position.csv"
        
        if not os.path.exists(cta_file):
            logger.warning(f"⚠️ CTA数据文件不存在: {cta_file}")
            return False
        
        if not os.path.exists(position_file):
            logger.warning(f"⚠️ 持仓数据文件不存在: {position_file}")
            return False
        
        # 加载数据，尝试多种编码
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'latin1']

        cta_data = None
        position_data = None

        for encoding in encodings:
            try:
                cta_data = pd.read_csv(cta_file, encoding=encoding)
                logger.info(f"CTA数据使用编码 {encoding} 加载成功")
                break
            except UnicodeDecodeError:
                continue

        if cta_data is None:
            logger.error("无法使用任何编码加载CTA数据")
            return False

        for encoding in encodings:
            try:
                position_data = pd.read_csv(position_file, encoding=encoding)
                logger.info(f"持仓数据使用编码 {encoding} 加载成功")
                break
            except UnicodeDecodeError:
                continue

        if position_data is None:
            logger.error("无法使用任何编码加载持仓数据")
            return False
        
        logger.info(f"✅ 数据加载测试通过")
        logger.info(f"  - CTA数据: {len(cta_data)} 条记录")
        logger.info(f"  - 持仓数据: {len(position_data)} 条记录")
        logger.info(f"  - CTA数据列: {list(cta_data.columns)}")
        logger.info(f"  - 持仓数据列: {list(position_data.columns)}")
        
        return cta_data, position_data
        
    except Exception as e:
        logger.error(f"❌ 数据加载测试失败: {e}")
        return False


def test_analysis_modules(cta_data, position_data):
    """测试分析模块"""
    logger.info("🔍 测试分析模块...")
    
    try:
        from config.modular_config_manager import ModularConfigManager
        from analysis.comprehensive_analyzer import ComprehensiveAnalyzer
        
        # 初始化配置和分析器
        config_manager = ModularConfigManager()
        config_manager.initialize()
        
        master_config = config_manager.get_master_config()
        analyzer = ComprehensiveAnalyzer(master_config)
        
        # 运行综合分析
        logger.info("运行综合分析...")
        analysis_results = analyzer.run_comprehensive_analysis(cta_data, position_data)
        
        # 检查分析结果
        modules_tested = []
        modules_failed = []
        
        expected_modules = [
            'pnl_analysis', 'position_analysis', 'return_rate_analysis',
            'performance_analysis', 'risk_analysis', 'contribution_analysis'
        ]
        
        for module in expected_modules:
            if module in analysis_results and analysis_results[module]:
                modules_tested.append(module)
                logger.info(f"  ✅ {module}: 成功")
            else:
                modules_failed.append(module)
                logger.warning(f"  ❌ {module}: 失败")
        
        logger.info(f"✅ 分析模块测试完成")
        logger.info(f"  - 成功模块: {len(modules_tested)}/{len(expected_modules)}")
        logger.info(f"  - 失败模块: {modules_failed}")
        
        return analysis_results, len(modules_failed) == 0
        
    except Exception as e:
        logger.error(f"❌ 分析模块测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None, False


def test_quantstats_integration():
    """测试quantstats集成"""
    logger.info("📈 测试quantstats集成...")
    
    try:
        from analysis.quantstats_integration import QuantstatsIntegrator
        
        # 创建测试数据
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=100, freq='D')
        returns = pd.Series(np.random.normal(0.001, 0.02, 100), index=dates)
        
        # 初始化quantstats集成器
        integrator = QuantstatsIntegrator()
        
        # 测试性能指标计算
        perf_metrics = integrator.calculate_performance_metrics(returns)
        
        # 测试风险指标计算
        risk_metrics = integrator.calculate_risk_metrics(returns)
        
        logger.info(f"✅ Quantstats集成测试通过")
        logger.info(f"  - 性能指标: {len(perf_metrics)} 个")
        logger.info(f"  - 风险指标: {len(risk_metrics)} 个")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Quantstats集成测试失败: {e}")
        return False


def test_multi_timeframe_reporting():
    """测试多时间维度报告"""
    logger.info("📋 测试多时间维度报告...")
    
    try:
        from reporting.multi_timeframe_reporter import MultiTimeframeReporter
        from config.modular_config_manager import ModularConfigManager
        
        # 初始化配置和报告器
        config_manager = ModularConfigManager()
        config_manager.initialize()
        
        reporter = MultiTimeframeReporter(config_manager)
        
        # 测试时间维度配置
        enabled_timeframes = reporter._get_enabled_timeframes()
        
        # 测试时间维度配置获取
        daily_config = reporter._get_timeframe_config('daily')
        weekly_config = reporter._get_timeframe_config('weekly')
        
        logger.info(f"✅ 多时间维度报告测试通过")
        logger.info(f"  - 启用的时间维度: {enabled_timeframes}")
        logger.info(f"  - 日报配置: {'已加载' if daily_config else '未加载'}")
        logger.info(f"  - 周报配置: {'已加载' if weekly_config else '未加载'}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多时间维度报告测试失败: {e}")
        return False


def test_report_generation(analysis_results):
    """测试报告生成"""
    logger.info("📄 测试报告生成...")
    
    try:
        from reporting.excel_reporter import ExcelReporter
        from reporting.markdown_reporter import MarkdownReporter
        from config.modular_config_manager import ModularConfigManager
        
        # 初始化配置
        config_manager = ModularConfigManager()
        config_manager.initialize()
        master_config = config_manager.get_master_config()
        
        # 测试Excel报告生成
        excel_reporter = ExcelReporter(master_config)
        excel_file = excel_reporter.generate_comprehensive_report(
            performance_results=analysis_results.get('performance_analysis', {}),
            risk_results=analysis_results.get('risk_analysis', {}),
            contribution_results=analysis_results.get('contribution_analysis', {}),
            output_file="output/reports/excel/integration_test_report.xlsx"
        )
        
        # 测试Markdown报告生成
        markdown_reporter = MarkdownReporter(master_config)
        markdown_file = markdown_reporter.generate_comprehensive_report(
            analysis_results, 
            output_file="output/reports/markdown/integration_test_report.md"
        )
        
        logger.info(f"✅ 报告生成测试通过")
        logger.info(f"  - Excel报告: {excel_file}")
        logger.info(f"  - Markdown报告: {markdown_file}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 报告生成测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False


def main():
    """主测试函数"""
    logger.info("🚀 开始CTA策略分析系统集成测试")
    logger.info("=" * 60)
    
    test_results = {}
    
    # 1. 测试输出管理器
    test_results['output_manager'] = test_output_manager()
    
    # 2. 测试配置系统
    test_results['configuration'] = test_configuration_system()
    
    # 3. 测试数据加载
    data_result = test_data_loading()
    if data_result:
        cta_data, position_data = data_result
        test_results['data_loading'] = True
    else:
        test_results['data_loading'] = False
        cta_data, position_data = None, None
    
    # 4. 测试quantstats集成
    test_results['quantstats'] = test_quantstats_integration()
    
    # 5. 测试多时间维度报告
    test_results['multi_timeframe'] = test_multi_timeframe_reporting()
    
    # 6. 测试分析模块（如果数据加载成功）
    if cta_data is not None and position_data is not None:
        analysis_results, analysis_success = test_analysis_modules(cta_data, position_data)
        test_results['analysis_modules'] = analysis_success
        
        # 7. 测试报告生成（如果分析成功）
        if analysis_success and analysis_results:
            test_results['report_generation'] = test_report_generation(analysis_results)
        else:
            test_results['report_generation'] = False
    else:
        test_results['analysis_modules'] = False
        test_results['report_generation'] = False
    
    # 生成测试总结
    logger.info("=" * 60)
    logger.info("🎯 系统集成测试总结")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"")
    logger.info(f"总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！系统已准备就绪。")
        return 0
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败，请检查相关模块。")
        return 1


if __name__ == "__main__":
    exit(main())
