# PnL Analysis Module Configuration
# 
# This configuration file contains all settings specific to the PnL analysis module
# including classification fields, pivot settings, and custom calculations.

# ================================
# PnL Analysis Configuration
# ================================
pnl_analysis:
  # Primary classification fields for PnL analysis
  # These fields are used as the main grouping dimensions
  primary_classification_fields:
    - "strategy_category"
    - "strategy_signal" 
    - "signal_freq"
  
  # Pivot fields for cross-tabulation analysis
  # These fields are used to create pivot tables for detailed analysis
  pivot_fields:
    - "position_direction"
    - "symbol_category"
    - "industry"
  
  # Custom calculation settings
  custom_calculations:
    # Enable/disable custom symbol category calculations
    enable_custom_symbol_category: true
    
    # Enable/disable custom industry calculations
    enable_custom_industry: true
    
    # Custom symbol category groups
    # Define custom groupings of symbols for specialized analysis
    custom_symbol_groups:
      "金属类":
        - "cu"    # 铜
        - "al"    # 铝
        - "zn"    # 锌
        - "pb"    # 铅
        - "ni"    # 镍
        - "sn"    # 锡
      
      "农产品类":
        - "a"     # 豆一
        - "c"     # 玉米
        - "m"     # 豆粕
        - "y"     # 豆油
        - "p"     # 棕榈油
        - "cf"    # 棉花
        - "sr"    # 白糖
        - "rm"    # 菜粕
        - "oi"    # 菜油
      
      "化工类":
        - "eb"    # 苯乙烯
        - "eg"    # 乙二醇
        - "pp"    # 聚丙烯
        - "ta"    # PTA
        - "ma"    # 甲醇
        - "pu"    # 聚氨酯
        - "v"     # PVC
        - "l"     # 聚乙烯
      
      "能源类":
        - "fu"    # 燃料油
        - "sc"    # 原油
        - "lu"    # 低硫燃料油
        - "pg"    # 液化石油气
      
      "黑色系":
        - "rb"    # 螺纹钢
        - "hc"    # 热卷
        - "i"     # 铁矿石
        - "j"     # 焦炭
        - "jm"    # 焦煤
        - "sf"    # 硅铁
        - "sm"    # 锰硅
    
    # Custom industry groups
    # Define custom groupings of industries for specialized analysis
    custom_industry_groups:
      "有色金属":
        - "有色金属"
        - "贵金属"
        - "稀有金属"
      
      "农林牧渔":
        - "农产品"
        - "饲料"
        - "农业"
        - "林业"
        - "畜牧业"
        - "渔业"
      
      "石油化工":
        - "化工"
        - "石油"
        - "天然气"
        - "煤炭"
        - "化纤"
      
      "钢铁建材":
        - "钢铁"
        - "建材"
        - "水泥"
        - "玻璃"
        - "建筑"
      
      "金融服务":
        - "银行"
        - "保险"
        - "证券"
        - "期货"

# ================================
# Analysis Output Configuration
# ================================
output:
  # Output format settings
  formats:
    - "dataframe"    # Return as pandas DataFrame
    - "dict"         # Return as dictionary
    - "json"         # Export as JSON file
  
  # Sorting and filtering options
  sorting:
    # Default sort field for results
    default_sort_field: "profit_loss_amount"
    
    # Default sort order (ascending/descending)
    default_sort_order: "descending"
    
    # Enable/disable automatic sorting
    auto_sort: true
  
  # Aggregation settings
  aggregation:
    # Default aggregation function for PnL
    default_function: "sum"
    
    # Additional aggregation functions to calculate
    additional_functions:
      - "mean"
      - "count"
      - "std"
      - "min"
      - "max"
  
  # Precision settings
  precision:
    # Decimal places for monetary amounts
    monetary_precision: 2
    
    # Decimal places for percentages
    percentage_precision: 4
    
    # Decimal places for ratios
    ratio_precision: 6

# ================================
# Data Validation Configuration
# ================================
validation:
  # Enable/disable data validation
  enabled: true
  
  # Required fields for PnL analysis
  required_fields:
    - "profit_loss_amount"
    - "strategy_category"
    - "trade_date"
  
  # Data quality checks
  quality_checks:
    # Check for missing values
    check_missing_values: true
    
    # Check for outliers
    check_outliers: true
    
    # Outlier detection method
    outlier_method: "iqr"  # "iqr", "zscore", "isolation_forest"
    
    # Outlier threshold (for IQR method)
    outlier_threshold: 3.0
  
  # Data type validation
  data_types:
    profit_loss_amount: "numeric"
    strategy_category: "string"
    trade_date: "datetime"
    position_direction: "string"
    symbol_category: "string"
    industry: "string"

# ================================
# Performance Configuration
# ================================
performance:
  # Enable/disable performance optimizations
  optimizations_enabled: true
  
  # Caching settings
  caching:
    # Enable result caching
    enabled: true
    
    # Cache expiry time (seconds)
    expiry_time: 3600
    
    # Maximum cache size (MB)
    max_size: 100
  
  # Parallel processing settings
  parallel_processing:
    # Enable parallel processing for large datasets
    enabled: true
    
    # Number of worker processes (0 = auto-detect)
    num_workers: 0
    
    # Minimum data size for parallel processing
    min_data_size: 10000

# ================================
# Logging Configuration
# ================================
logging:
  # Log level for PnL analysis module
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL
  
  # Enable detailed logging
  detailed_logging: true
  
  # Log performance metrics
  log_performance: true
  
  # Log data quality issues
  log_data_quality: true
