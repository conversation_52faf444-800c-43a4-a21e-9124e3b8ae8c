"""
CTA策略收益贡献分析模块
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
if current_dir.name != 'cta_analysis_system':
    project_root = current_dir.parent
else:
    project_root = current_dir
sys.path.insert(0, str(project_root))


import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta

from config.settings import CTAConfig
from utils.time_window_calculator import TimeWindowCalculator

logger = logging.getLogger(__name__)


class ContributionAnalyzer:
    """收益贡献分析器"""

    def __init__(self, config: CTAConfig):
        self.config = config
        self.time_calculator = TimeWindowCalculator(config)  # 时间窗口计算器
    
    def calculate_strategy_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """计算策略类别收益贡献"""
        logger.info("Calculating strategy contributions...")
        
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column
        
        # 按策略类别聚合PnL
        strategy_pnl = cta_data.groupby(strategy_col)[pnl_col].sum()
        total_pnl = strategy_pnl.sum()
        
        # 分离盈利和亏损策略
        profit_strategies = strategy_pnl[strategy_pnl > 0]
        loss_strategies = strategy_pnl[strategy_pnl < 0]
        
        total_gain = profit_strategies.sum() if len(profit_strategies) > 0 else 0
        total_loss = abs(loss_strategies.sum()) if len(loss_strategies) > 0 else 0
        
        # 计算贡献百分比（使用修改后的方法）
        contributions = {}

        # 安全获取percentage_method
        try:
            if hasattr(self.config.analysis, 'contribution_analysis'):
                if hasattr(self.config.analysis.contribution_analysis, 'percentage_method'):
                    percentage_method = self.config.analysis.contribution_analysis.percentage_method
                else:
                    percentage_method = "modified"
            else:
                percentage_method = "modified"
        except:
            percentage_method = "modified"
        
        for strategy, pnl in strategy_pnl.items():
            if percentage_method == "modified":
                # 修改后的百分比计算方法
                if total_pnl > 0:  # 总体盈利
                    if total_gain > 0:
                        percentage = (pnl / total_gain) * 100
                    else:
                        percentage = 0
                else:  # 总体亏损
                    if total_loss > 0:
                        percentage = (pnl / -total_loss) * 100  # 保持原始符号
                    else:
                        percentage = 0
            else:
                # 标准百分比计算方法
                percentage = (pnl / total_pnl) * 100 if total_pnl != 0 else 0
            
            contributions[strategy] = {
                "pnl": pnl,
                "percentage": percentage,
                "is_profit": pnl > 0
            }
        
        return {
            "strategy_contributions": contributions,
            "total_pnl": total_pnl,
            "total_gain": total_gain,
            "total_loss": total_loss,
            "profit_strategies": profit_strategies.to_dict(),
            "loss_strategies": loss_strategies.to_dict(),
            "calculation_method": percentage_method
        }
    
    def calculate_time_window_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """计算不同时间窗口的收益贡献 - 使用日历期间"""
        logger.info("Calculating time window contributions with calendar periods...")

        results = {}
        date_col = self.config.data.date_column

        # 确保日期列是datetime格式
        if not pd.api.types.is_datetime64_any_dtype(cta_data[date_col]):
            cta_data = cta_data.copy()
            cta_data[date_col] = pd.to_datetime(cta_data[date_col])

        latest_date = cta_data[date_col].max()

        # 获取所有时间窗口信息
        time_windows_info = self.time_calculator.get_all_time_windows(latest_date)

        logger.info(f"Time window calculation for contributions:\n{self.time_calculator.format_time_window_summary(latest_date)}")

        for window_name, window_info in time_windows_info.items():
            start_date = window_info['start_date']
            end_date = window_info['end_date']

            # 过滤时间窗口数据
            window_data = cta_data[(cta_data[date_col] >= start_date) & (cta_data[date_col] <= end_date)]

            # 验证时间窗口数据
            validation = self.time_calculator.validate_time_window_data(
                cta_data, date_col, start_date, end_date
            )

            if not validation['valid']:
                logger.warning(f"Invalid data for {window_name} contributions: {validation['error']}")
                continue

            window_contributions = self.calculate_strategy_contributions(window_data)

            # 添加时间窗口元数据
            window_contributions.update({
                "time_window_info": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "description": window_info['description'],
                    "calculation_method": self.time_calculator._get_calculation_method(),
                    "data_validation": validation
                }
            })

            results[window_name] = window_contributions

        return results
    
    def calculate_industry_contributions(self, cta_data: pd.DataFrame, by_strategy: bool = True) -> Dict[str, Any]:
        """计算行业收益贡献"""
        logger.info("Calculating industry contributions...")
        
        if self.config.data.industry_column not in cta_data.columns:
            logger.warning("Industry column not found in data")
            return {}
        
        industry_col = self.config.data.industry_column
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        
        results = {}
        
        if by_strategy:
            # 按策略类别分析各行业贡献
            for strategy in cta_data[strategy_col].unique():
                strategy_data = cta_data[cta_data[strategy_col] == strategy]
                industry_pnl = strategy_data.groupby(industry_col)[pnl_col].sum()
                total_strategy_pnl = industry_pnl.sum()
                
                industry_contributions = {}
                for industry, pnl in industry_pnl.items():
                    percentage = (pnl / total_strategy_pnl) * 100 if total_strategy_pnl != 0 else 0
                    industry_contributions[industry] = {
                        "pnl": pnl,
                        "percentage": percentage,
                        "is_profit": pnl > 0
                    }
                
                results[strategy] = {
                    "industry_contributions": industry_contributions,
                    "total_pnl": total_strategy_pnl
                }
        else:
            # 整体行业贡献分析
            industry_pnl = cta_data.groupby(industry_col)[pnl_col].sum()
            total_pnl = industry_pnl.sum()
            
            industry_contributions = {}
            for industry, pnl in industry_pnl.items():
                percentage = (pnl / total_pnl) * 100 if total_pnl != 0 else 0
                industry_contributions[industry] = {
                    "pnl": pnl,
                    "percentage": percentage,
                    "is_profit": pnl > 0
                }
            
            results = {
                "industry_contributions": industry_contributions,
                "total_pnl": total_pnl
            }
        
        return results
    
    def calculate_signal_frequency_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """计算信号频率收益贡献"""
        logger.info("Calculating signal frequency contributions...")
        
        if self.config.data.signal_freq_column not in cta_data.columns:
            logger.warning("Signal frequency column not found in data")
            return {}
        
        freq_col = self.config.data.signal_freq_column
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        
        results = {}
        
        # 按策略类别分析各频率贡献
        for strategy in cta_data[strategy_col].unique():
            strategy_data = cta_data[cta_data[strategy_col] == strategy]
            
            if freq_col in strategy_data.columns:
                freq_pnl = strategy_data.groupby(freq_col)[pnl_col].sum()
                total_strategy_pnl = freq_pnl.sum()
                
                freq_contributions = {}
                for freq, pnl in freq_pnl.items():
                    percentage = (pnl / total_strategy_pnl) * 100 if total_strategy_pnl != 0 else 0
                    freq_contributions[freq] = {
                        "pnl": pnl,
                        "percentage": percentage,
                        "is_profit": pnl > 0
                    }
                
                results[strategy] = {
                    "frequency_contributions": freq_contributions,
                    "total_pnl": total_strategy_pnl
                }
        
        return results
    
    def calculate_hierarchical_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """计算层级化收益贡献"""
        logger.info("Calculating hierarchical contributions...")
        
        strategy_col = self.config.data.strategy_category_column
        signal_col = self.config.data.strategy_signal_column
        freq_col = self.config.data.signal_freq_column
        industry_col = self.config.data.industry_column
        pnl_col = self.config.data.pnl_column
        
        results = {
            "level_1_strategy_category": {},
            "level_2_signal_details": {},
            "level_3_industry_breakdown": {}
        }
        
        # 第一层：策略类别
        strategy_contributions = self.calculate_strategy_contributions(cta_data)
        results["level_1_strategy_category"] = strategy_contributions
        
        # 第二层：trend策略的信号和频率细分
        trend_data = cta_data[cta_data[strategy_col] == 'trend']
        if not trend_data.empty:
            trend_details = {}
            
            # 按信号分析
            if signal_col in trend_data.columns:
                signal_pnl = trend_data.groupby(signal_col)[pnl_col].sum()
                total_trend_pnl = signal_pnl.sum()
                
                for signal, pnl in signal_pnl.items():
                    percentage = (pnl / total_trend_pnl) * 100 if total_trend_pnl != 0 else 0
                    trend_details[f"signal_{signal}"] = {
                        "pnl": pnl,
                        "percentage": percentage,
                        "is_profit": pnl > 0
                    }
            
            # 按频率分析
            if freq_col in trend_data.columns:
                freq_pnl = trend_data.groupby(freq_col)[pnl_col].sum()
                total_trend_pnl = freq_pnl.sum()
                
                for freq, pnl in freq_pnl.items():
                    percentage = (pnl / total_trend_pnl) * 100 if total_trend_pnl != 0 else 0
                    trend_details[f"frequency_{freq}"] = {
                        "pnl": pnl,
                        "percentage": percentage,
                        "is_profit": pnl > 0
                    }
            
            results["level_2_signal_details"]["trend"] = trend_details
        
        # 第三层：各策略类别下的行业分析
        if industry_col in cta_data.columns:
            industry_contributions = self.calculate_industry_contributions(cta_data, by_strategy=True)
            results["level_3_industry_breakdown"] = industry_contributions
        
        return results
    
    def calculate_marginal_contributions(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """计算边际贡献分析"""
        logger.info("Calculating marginal contributions...")
        
        strategy_col = self.config.data.strategy_category_column
        pnl_col = self.config.data.pnl_column
        date_col = self.config.data.date_column
        
        results = {}
        
        # 计算每个策略的边际贡献（移除该策略后的组合表现变化）
        total_daily_pnl = cta_data.groupby(date_col)[pnl_col].sum()
        total_performance = {
            "total_pnl": total_daily_pnl.sum(),
            "volatility": total_daily_pnl.std(),
            "sharpe_ratio": total_daily_pnl.mean() / total_daily_pnl.std() if total_daily_pnl.std() != 0 else 0
        }
        
        for strategy in cta_data[strategy_col].unique():
            # 移除该策略后的数据
            remaining_data = cta_data[cta_data[strategy_col] != strategy]
            
            if remaining_data.empty:
                continue
            
            remaining_daily_pnl = remaining_data.groupby(date_col)[pnl_col].sum()
            remaining_performance = {
                "total_pnl": remaining_daily_pnl.sum(),
                "volatility": remaining_daily_pnl.std(),
                "sharpe_ratio": remaining_daily_pnl.mean() / remaining_daily_pnl.std() if remaining_daily_pnl.std() != 0 else 0
            }
            
            # 计算边际贡献
            marginal_contribution = {
                "pnl_contribution": total_performance["total_pnl"] - remaining_performance["total_pnl"],
                "volatility_contribution": total_performance["volatility"] - remaining_performance["volatility"],
                "sharpe_contribution": total_performance["sharpe_ratio"] - remaining_performance["sharpe_ratio"]
            }
            
            results[strategy] = marginal_contribution
        
        return {
            "marginal_contributions": results,
            "total_performance": total_performance
        }
    
    def generate_contribution_summary(self, contribution_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成贡献分析摘要"""
        logger.info("Generating contribution summary...")
        
        summary = {
            "top_contributors": [],
            "bottom_contributors": [],
            "contribution_concentration": 0,
            "diversification_score": 0
        }
        
        # 从策略贡献中提取信息
        if "strategy_contributions" in contribution_results:
            # contribution_results["strategy_contributions"] 包含完整的策略贡献数据
            strategy_contrib_data = contribution_results["strategy_contributions"]
            strategy_contribs = strategy_contrib_data["strategy_contributions"]
            
            # 按贡献排序
            sorted_contribs = sorted(strategy_contribs.items(), 
                                   key=lambda x: x[1]["pnl"], reverse=True)
            
            # 前三大贡献者
            summary["top_contributors"] = [
                {"strategy": strategy, "pnl": data["pnl"], "percentage": data["percentage"]}
                for strategy, data in sorted_contribs[:3]
            ]
            
            # 后三大贡献者
            summary["bottom_contributors"] = [
                {"strategy": strategy, "pnl": data["pnl"], "percentage": data["percentage"]}
                for strategy, data in sorted_contribs[-3:]
            ]
            
            # 计算贡献集中度（最大贡献者的占比）
            if sorted_contribs:
                max_contribution = abs(sorted_contribs[0][1]["percentage"])
                summary["contribution_concentration"] = max_contribution
            
            # 计算分散化得分（基于贡献的标准差）
            percentages = [abs(data["percentage"]) for _, data in strategy_contribs.items()]
            if len(percentages) > 1:
                summary["diversification_score"] = 100 - np.std(percentages)
            else:
                summary["diversification_score"] = 0
        
        return summary
