2025-06-17 02:45:37,055 - __main__ - INFO - 🚀 开始CTA策略分析系统集成测试
2025-06-17 02:45:37,055 - __main__ - INFO - ============================================================
2025-06-17 02:45:37,055 - __main__ - INFO - 🔧 测试输出管理器...
2025-06-17 02:45:37,057 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:45:37,057 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:45:37,057 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:45:37,058 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:45:37,058 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:45:37,058 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:45:37,058 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:45:37,058 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:45:37,059 - utils.output_manager - INFO - 文件已保存: output/reports/markdown/test_integration_20250617_024537.md
2025-06-17 02:45:37,059 - __main__ - INFO - ✅ 输出管理器测试通过
2025-06-17 02:45:37,059 - __main__ - INFO -   - 图表路径: output/charts/performance
2025-06-17 02:45:37,059 - __main__ - INFO -   - 报告路径: output/reports/excel
2025-06-17 02:45:37,059 - __main__ - INFO -   - 生成文件名: test_report_20250617_024537.xlsx
2025-06-17 02:45:37,059 - __main__ - INFO -   - 保存文件: output/reports/markdown/test_integration_20250617_024537.md
2025-06-17 02:45:37,059 - __main__ - INFO - 🔧 测试配置系统...
2025-06-17 02:45:37,093 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:45:37,096 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:45:37,099 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:45:37,104 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:45:37,108 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:45:37,115 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:45:37,115 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:45:37,115 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:45:37,115 - config.modular_config_manager - INFO - Configuration validation completed. Valid: True
2025-06-17 02:45:37,115 - __main__ - INFO - ✅ 配置系统测试通过
2025-06-17 02:45:37,115 - __main__ - INFO -   - PnL配置: 已加载
2025-06-17 02:45:37,115 - __main__ - INFO -   - 风险配置: 已加载
2025-06-17 02:45:37,115 - __main__ - INFO -   - 配置验证: 通过
2025-06-17 02:45:37,115 - __main__ - INFO - 📊 测试数据加载...
2025-06-17 02:45:37,121 - __main__ - ERROR - ❌ 数据加载测试失败: 'utf-8' codec can't decode byte 0xbb in position 621: invalid start byte
2025-06-17 02:45:37,121 - __main__ - INFO - 📈 测试quantstats集成...
2025-06-17 02:45:38,241 - analysis.quantstats_integration - INFO - 使用quantstats计算性能指标
2025-06-17 02:45:38,290 - analysis.quantstats_integration - INFO - Calculated 20 performance metrics
2025-06-17 02:45:38,290 - analysis.quantstats_integration - INFO - Calculating risk metrics with quantstats
2025-06-17 02:45:38,299 - __main__ - INFO - ✅ Quantstats集成测试通过
2025-06-17 02:45:38,299 - __main__ - INFO -   - 性能指标: 20 个
2025-06-17 02:45:38,299 - __main__ - INFO -   - 风险指标: 14 个
2025-06-17 02:45:38,299 - __main__ - INFO - 📋 测试多时间维度报告...
2025-06-17 02:45:38,367 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:45:38,370 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:45:38,373 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:45:38,377 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:45:38,382 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:45:38,389 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:45:38,389 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:45:38,389 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:45:38,389 - analysis.performance_analyzer - INFO - Quantstats integration enabled
2025-06-17 02:45:38,389 - analysis.risk_analyzer - INFO - Quantstats integration enabled for risk analysis
2025-06-17 02:45:38,389 - __main__ - INFO - ✅ 多时间维度报告测试通过
2025-06-17 02:45:38,389 - __main__ - INFO -   - 启用的时间维度: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom']
2025-06-17 02:45:38,389 - __main__ - INFO -   - 日报配置: 已加载
2025-06-17 02:45:38,389 - __main__ - INFO -   - 周报配置: 已加载
2025-06-17 02:45:38,389 - __main__ - INFO - ============================================================
2025-06-17 02:45:38,389 - __main__ - INFO - 🎯 系统集成测试总结
2025-06-17 02:45:38,389 - __main__ - INFO -   output_manager: ✅ 通过
2025-06-17 02:45:38,389 - __main__ - INFO -   configuration: ✅ 通过
2025-06-17 02:45:38,389 - __main__ - INFO -   data_loading: ❌ 失败
2025-06-17 02:45:38,389 - __main__ - INFO -   quantstats: ✅ 通过
2025-06-17 02:45:38,389 - __main__ - INFO -   multi_timeframe: ✅ 通过
2025-06-17 02:45:38,389 - __main__ - INFO -   analysis_modules: ❌ 失败
2025-06-17 02:45:38,389 - __main__ - INFO -   report_generation: ❌ 失败
2025-06-17 02:45:38,389 - __main__ - INFO - 
2025-06-17 02:45:38,389 - __main__ - INFO - 总体结果: 4/7 测试通过
2025-06-17 02:45:38,389 - __main__ - WARNING - ⚠️ 3 个测试失败，请检查相关模块。
2025-06-17 02:46:02,417 - __main__ - INFO - 🚀 开始CTA策略分析系统集成测试
2025-06-17 02:46:02,417 - __main__ - INFO - ============================================================
2025-06-17 02:46:02,417 - __main__ - INFO - 🔧 测试输出管理器...
2025-06-17 02:46:02,418 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:46:02,418 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:46:02,418 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:46:02,418 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:46:02,418 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:46:02,419 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:46:02,419 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:46:02,419 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:46:02,419 - utils.output_manager - INFO - 文件已保存: output/reports/markdown/test_integration_20250617_024602.md
2025-06-17 02:46:02,419 - __main__ - INFO - ✅ 输出管理器测试通过
2025-06-17 02:46:02,419 - __main__ - INFO -   - 图表路径: output/charts/performance
2025-06-17 02:46:02,419 - __main__ - INFO -   - 报告路径: output/reports/excel
2025-06-17 02:46:02,419 - __main__ - INFO -   - 生成文件名: test_report_20250617_024602.xlsx
2025-06-17 02:46:02,419 - __main__ - INFO -   - 保存文件: output/reports/markdown/test_integration_20250617_024602.md
2025-06-17 02:46:02,419 - __main__ - INFO - 🔧 测试配置系统...
2025-06-17 02:46:02,452 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:02,455 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:02,458 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:02,462 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:02,467 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:02,474 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:02,474 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:02,474 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:02,474 - config.modular_config_manager - INFO - Configuration validation completed. Valid: True
2025-06-17 02:46:02,474 - __main__ - INFO - ✅ 配置系统测试通过
2025-06-17 02:46:02,474 - __main__ - INFO -   - PnL配置: 已加载
2025-06-17 02:46:02,474 - __main__ - INFO -   - 风险配置: 已加载
2025-06-17 02:46:02,474 - __main__ - INFO -   - 配置验证: 通过
2025-06-17 02:46:02,474 - __main__ - INFO - 📊 测试数据加载...
2025-06-17 02:46:02,770 - __main__ - INFO - CTA数据使用编码 gbk 加载成功
2025-06-17 02:46:02,771 - __main__ - INFO - 持仓数据使用编码 utf-8-sig 加载成功
2025-06-17 02:46:02,771 - __main__ - INFO - ✅ 数据加载测试通过
2025-06-17 02:46:02,771 - __main__ - INFO -   - CTA数据: 114900 条记录
2025-06-17 02:46:02,771 - __main__ - INFO -   - 持仓数据: 350 条记录
2025-06-17 02:46:02,771 - __main__ - INFO -   - CTA数据列: ['cx_id', 'trade_date', 'product_name', 'symbol', 'strategy_name_category', 'position_direction', 'symbol_category', 'strategy_name', 'contract_unit', 'profit_loss_amount', 'position_profit_loss_amount', 'close_out_profit_loss_amount', 'position_amount', 'position_quantity', 'pre_settlement_price', 'settlement_price', 'transaction_commission', 'gateway_name', 'create_date', 'update_date', 'strategy_signal', 'signal_freq', 'strategy_category', 'code', 'name', 'industry']
2025-06-17 02:46:02,771 - __main__ - INFO -   - 持仓数据列: ['date', 'other', 'option', 'trend', 'all_positon', 'fin', 'boll', 'rsi']
2025-06-17 02:46:02,771 - __main__ - INFO - 📈 测试quantstats集成...
2025-06-17 02:46:03,786 - analysis.quantstats_integration - INFO - 使用quantstats计算性能指标
2025-06-17 02:46:03,834 - analysis.quantstats_integration - INFO - Calculated 20 performance metrics
2025-06-17 02:46:03,834 - analysis.quantstats_integration - INFO - Calculating risk metrics with quantstats
2025-06-17 02:46:03,843 - __main__ - INFO - ✅ Quantstats集成测试通过
2025-06-17 02:46:03,843 - __main__ - INFO -   - 性能指标: 20 个
2025-06-17 02:46:03,843 - __main__ - INFO -   - 风险指标: 14 个
2025-06-17 02:46:03,843 - __main__ - INFO - 📋 测试多时间维度报告...
2025-06-17 02:46:03,902 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:03,905 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:03,908 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:03,912 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:03,917 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:03,924 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:03,924 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:03,924 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:03,924 - analysis.performance_analyzer - INFO - Quantstats integration enabled
2025-06-17 02:46:03,924 - analysis.risk_analyzer - INFO - Quantstats integration enabled for risk analysis
2025-06-17 02:46:03,924 - __main__ - INFO - ✅ 多时间维度报告测试通过
2025-06-17 02:46:03,924 - __main__ - INFO -   - 启用的时间维度: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom']
2025-06-17 02:46:03,924 - __main__ - INFO -   - 日报配置: 已加载
2025-06-17 02:46:03,924 - __main__ - INFO -   - 周报配置: 已加载
2025-06-17 02:46:03,924 - __main__ - INFO - 🔍 测试分析模块...
2025-06-17 02:46:03,930 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:03,933 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:03,937 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:03,941 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:03,945 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:03,952 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:03,952 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:03,952 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:03,952 - analysis.performance_analyzer - INFO - Quantstats integration enabled
2025-06-17 02:46:03,952 - analysis.risk_analyzer - INFO - Quantstats integration enabled for risk analysis
2025-06-17 02:46:03,952 - __main__ - INFO - 运行综合分析...
2025-06-17 02:46:03,952 - analysis.comprehensive_analyzer - INFO - Starting comprehensive CTA analysis
2025-06-17 02:46:03,952 - analysis.comprehensive_analyzer - INFO - Running PnL analysis...
2025-06-17 02:46:03,952 - analysis.pnl_analyzer - INFO - Running comprehensive PnL analysis
2025-06-17 02:46:03,954 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: strategy_category
2025-06-17 02:46:04,024 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: strategy_signal
2025-06-17 02:46:04,085 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: signal_freq
2025-06-17 02:46:04,144 - analysis.pnl_analyzer - INFO - Running custom PnL calculations
2025-06-17 02:46:04,166 - analysis.pnl_analyzer - INFO - Comprehensive PnL analysis completed successfully
2025-06-17 02:46:04,166 - analysis.comprehensive_analyzer - INFO - Running position analysis...
2025-06-17 02:46:04,166 - analysis.position_analyzer - INFO - Analyzing current positions...
2025-06-17 02:46:04,166 - analysis.comprehensive_analyzer - ERROR - Position analysis failed: 'types.SimpleNamespace' object has no attribute 'date_column'
2025-06-17 02:46:04,166 - analysis.comprehensive_analyzer - INFO - Running return rate calculation...
2025-06-17 02:46:04,166 - analysis.return_rate_calculator - INFO - Calculating strategy category returns
2025-06-17 02:46:04,166 - analysis.return_rate_calculator - INFO - Processing strategy: other -> position field: other
2025-06-17 02:46:04,176 - analysis.return_rate_calculator - INFO - Processing strategy: option -> position field: option
2025-06-17 02:46:04,187 - analysis.return_rate_calculator - INFO - Processing strategy: trend -> position field: trend
2025-06-17 02:46:04,205 - analysis.return_rate_calculator - INFO - Processing strategy: orderflow -> position field: other
2025-06-17 02:46:04,213 - analysis.return_rate_calculator - INFO - Calculating specific futures returns
2025-06-17 02:46:04,223 - analysis.return_rate_calculator - INFO - Calculating signal returns
2025-06-17 02:46:04,223 - analysis.return_rate_calculator - INFO - Processing signal: simple_boll -> position field: boll
2025-06-17 02:46:04,232 - analysis.return_rate_calculator - INFO - Processing signal: simple_rsi -> position field: rsi
2025-06-17 02:46:04,239 - analysis.return_rate_calculator - INFO - Saving return rate data
2025-06-17 02:46:04,239 - analysis.return_rate_calculator - ERROR - Failed to save return rate data: 'types.SimpleNamespace' object has no attribute 'output'
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - INFO - Running performance analysis...
2025-06-17 02:46:04,239 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - ERROR - Performance analysis failed: 'types.SimpleNamespace' object has no attribute 'strategy_category_column'
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - INFO - Running risk analysis...
2025-06-17 02:46:04,239 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-17 02:46:04,239 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - ERROR - Risk analysis failed: 'types.SimpleNamespace' object has no attribute 'risk_metrics'
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - INFO - Running contribution analysis...
2025-06-17 02:46:04,239 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - ERROR - Contribution analysis failed: 'types.SimpleNamespace' object has no attribute 'date_column'
2025-06-17 02:46:04,239 - analysis.comprehensive_analyzer - INFO - Comprehensive analysis completed successfully
2025-06-17 02:46:04,239 - __main__ - INFO -   ✅ pnl_analysis: 成功
2025-06-17 02:46:04,239 - __main__ - WARNING -   ❌ position_analysis: 失败
2025-06-17 02:46:04,239 - __main__ - INFO -   ✅ return_rate_analysis: 成功
2025-06-17 02:46:04,239 - __main__ - WARNING -   ❌ performance_analysis: 失败
2025-06-17 02:46:04,239 - __main__ - WARNING -   ❌ risk_analysis: 失败
2025-06-17 02:46:04,239 - __main__ - WARNING -   ❌ contribution_analysis: 失败
2025-06-17 02:46:04,239 - __main__ - INFO - ✅ 分析模块测试完成
2025-06-17 02:46:04,239 - __main__ - INFO -   - 成功模块: 2/6
2025-06-17 02:46:04,239 - __main__ - INFO -   - 失败模块: ['position_analysis', 'performance_analysis', 'risk_analysis', 'contribution_analysis']
2025-06-17 02:46:04,239 - __main__ - INFO - ============================================================
2025-06-17 02:46:04,239 - __main__ - INFO - 🎯 系统集成测试总结
2025-06-17 02:46:04,239 - __main__ - INFO -   output_manager: ✅ 通过
2025-06-17 02:46:04,239 - __main__ - INFO -   configuration: ✅ 通过
2025-06-17 02:46:04,239 - __main__ - INFO -   data_loading: ✅ 通过
2025-06-17 02:46:04,239 - __main__ - INFO -   quantstats: ✅ 通过
2025-06-17 02:46:04,239 - __main__ - INFO -   multi_timeframe: ✅ 通过
2025-06-17 02:46:04,239 - __main__ - INFO -   analysis_modules: ❌ 失败
2025-06-17 02:46:04,240 - __main__ - INFO -   report_generation: ❌ 失败
2025-06-17 02:46:04,240 - __main__ - INFO - 
2025-06-17 02:46:04,240 - __main__ - INFO - 总体结果: 5/7 测试通过
2025-06-17 02:46:04,240 - __main__ - WARNING - ⚠️ 2 个测试失败，请检查相关模块。
2025-06-17 02:46:33,502 - __main__ - INFO - 🚀 开始CTA策略分析系统集成测试
2025-06-17 02:46:33,503 - __main__ - INFO - ============================================================
2025-06-17 02:46:33,503 - __main__ - INFO - 🔧 测试输出管理器...
2025-06-17 02:46:33,503 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 正在初始化输出目录结构...
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 成功创建 19 个输出目录
2025-06-17 02:46:33,504 - utils.output_manager - INFO - 开始清理旧文件...
2025-06-17 02:46:33,505 - utils.output_manager - INFO - 文件清理完成: 处理 25 个目录, 删除 0 个文件, 保留 0 个文件, 0 个错误
2025-06-17 02:46:33,505 - utils.output_manager - INFO - 文件已保存: output/reports/markdown/test_integration_20250617_024633.md
2025-06-17 02:46:33,505 - __main__ - INFO - ✅ 输出管理器测试通过
2025-06-17 02:46:33,505 - __main__ - INFO -   - 图表路径: output/charts/performance
2025-06-17 02:46:33,505 - __main__ - INFO -   - 报告路径: output/reports/excel
2025-06-17 02:46:33,505 - __main__ - INFO -   - 生成文件名: test_report_20250617_024633.xlsx
2025-06-17 02:46:33,505 - __main__ - INFO -   - 保存文件: output/reports/markdown/test_integration_20250617_024633.md
2025-06-17 02:46:33,505 - __main__ - INFO - 🔧 测试配置系统...
2025-06-17 02:46:33,538 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:33,541 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:33,545 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:33,549 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:33,554 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:33,560 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:33,560 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:33,560 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:33,560 - config.modular_config_manager - INFO - Configuration validation completed. Valid: True
2025-06-17 02:46:33,560 - __main__ - INFO - ✅ 配置系统测试通过
2025-06-17 02:46:33,560 - __main__ - INFO -   - PnL配置: 已加载
2025-06-17 02:46:33,560 - __main__ - INFO -   - 风险配置: 已加载
2025-06-17 02:46:33,560 - __main__ - INFO -   - 配置验证: 通过
2025-06-17 02:46:33,560 - __main__ - INFO - 📊 测试数据加载...
2025-06-17 02:46:33,857 - __main__ - INFO - CTA数据使用编码 gbk 加载成功
2025-06-17 02:46:33,859 - __main__ - INFO - 持仓数据使用编码 utf-8-sig 加载成功
2025-06-17 02:46:33,859 - __main__ - INFO - ✅ 数据加载测试通过
2025-06-17 02:46:33,859 - __main__ - INFO -   - CTA数据: 114900 条记录
2025-06-17 02:46:33,859 - __main__ - INFO -   - 持仓数据: 350 条记录
2025-06-17 02:46:33,859 - __main__ - INFO -   - CTA数据列: ['cx_id', 'trade_date', 'product_name', 'symbol', 'strategy_name_category', 'position_direction', 'symbol_category', 'strategy_name', 'contract_unit', 'profit_loss_amount', 'position_profit_loss_amount', 'close_out_profit_loss_amount', 'position_amount', 'position_quantity', 'pre_settlement_price', 'settlement_price', 'transaction_commission', 'gateway_name', 'create_date', 'update_date', 'strategy_signal', 'signal_freq', 'strategy_category', 'code', 'name', 'industry']
2025-06-17 02:46:33,859 - __main__ - INFO -   - 持仓数据列: ['date', 'other', 'option', 'trend', 'all_positon', 'fin', 'boll', 'rsi']
2025-06-17 02:46:33,859 - __main__ - INFO - 📈 测试quantstats集成...
2025-06-17 02:46:34,921 - analysis.quantstats_integration - INFO - 使用quantstats计算性能指标
2025-06-17 02:46:34,970 - analysis.quantstats_integration - INFO - Calculated 20 performance metrics
2025-06-17 02:46:34,970 - analysis.quantstats_integration - INFO - Calculating risk metrics with quantstats
2025-06-17 02:46:34,978 - __main__ - INFO - ✅ Quantstats集成测试通过
2025-06-17 02:46:34,978 - __main__ - INFO -   - 性能指标: 20 个
2025-06-17 02:46:34,978 - __main__ - INFO -   - 风险指标: 14 个
2025-06-17 02:46:34,978 - __main__ - INFO - 📋 测试多时间维度报告...
2025-06-17 02:46:35,037 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:35,040 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:35,044 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:35,048 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:35,052 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:35,059 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:35,059 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:35,059 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:35,059 - analysis.performance_analyzer - INFO - Quantstats integration enabled
2025-06-17 02:46:35,060 - analysis.risk_analyzer - INFO - Quantstats integration enabled for risk analysis
2025-06-17 02:46:35,060 - __main__ - INFO - ✅ 多时间维度报告测试通过
2025-06-17 02:46:35,060 - __main__ - INFO -   - 启用的时间维度: ['daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom']
2025-06-17 02:46:35,060 - __main__ - INFO -   - 日报配置: 已加载
2025-06-17 02:46:35,060 - __main__ - INFO -   - 周报配置: 已加载
2025-06-17 02:46:35,060 - __main__ - INFO - 🔍 测试分析模块...
2025-06-17 02:46:35,066 - config.modular_config_manager - INFO - Loaded master configuration from config/master_config.yaml
2025-06-17 02:46:35,069 - config.modular_config_manager - INFO - Loaded module config: pnl_analysis
2025-06-17 02:46:35,072 - config.modular_config_manager - INFO - Loaded module config: position_analysis
2025-06-17 02:46:35,076 - config.modular_config_manager - INFO - Loaded module config: return_rate_calculation
2025-06-17 02:46:35,081 - config.modular_config_manager - INFO - Loaded module config: risk_analysis
2025-06-17 02:46:35,087 - config.modular_config_manager - INFO - Loaded module config: reporting
2025-06-17 02:46:35,088 - config.modular_config_manager - INFO - Loaded 5 module configurations
2025-06-17 02:46:35,088 - config.modular_config_manager - INFO - Modular configuration manager initialized successfully
2025-06-17 02:46:35,088 - analysis.performance_analyzer - INFO - Quantstats integration enabled
2025-06-17 02:46:35,088 - analysis.risk_analyzer - INFO - Quantstats integration enabled for risk analysis
2025-06-17 02:46:35,088 - __main__ - INFO - 运行综合分析...
2025-06-17 02:46:35,088 - analysis.comprehensive_analyzer - INFO - Starting comprehensive CTA analysis
2025-06-17 02:46:35,088 - analysis.comprehensive_analyzer - INFO - Running PnL analysis...
2025-06-17 02:46:35,088 - analysis.pnl_analyzer - INFO - Running comprehensive PnL analysis
2025-06-17 02:46:35,089 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: strategy_category
2025-06-17 02:46:35,159 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: strategy_signal
2025-06-17 02:46:35,220 - analysis.pnl_analyzer - INFO - Running classified PnL analysis with primary field: signal_freq
2025-06-17 02:46:35,278 - analysis.pnl_analyzer - INFO - Running custom PnL calculations
2025-06-17 02:46:35,300 - analysis.pnl_analyzer - INFO - Comprehensive PnL analysis completed successfully
2025-06-17 02:46:35,301 - analysis.comprehensive_analyzer - INFO - Running position analysis...
2025-06-17 02:46:35,301 - analysis.position_analyzer - INFO - Analyzing current positions...
2025-06-17 02:46:35,307 - analysis.comprehensive_analyzer - ERROR - Position analysis failed: 'types.SimpleNamespace' object has no attribute 'industry_column'
2025-06-17 02:46:35,307 - analysis.comprehensive_analyzer - INFO - Running return rate calculation...
2025-06-17 02:46:35,307 - analysis.return_rate_calculator - INFO - Calculating strategy category returns
2025-06-17 02:46:35,307 - analysis.return_rate_calculator - INFO - Processing strategy: other -> position field: other
2025-06-17 02:46:35,317 - analysis.return_rate_calculator - INFO - Processing strategy: option -> position field: option
2025-06-17 02:46:35,327 - analysis.return_rate_calculator - INFO - Processing strategy: trend -> position field: trend
2025-06-17 02:46:35,344 - analysis.return_rate_calculator - INFO - Processing strategy: orderflow -> position field: other
2025-06-17 02:46:35,351 - analysis.return_rate_calculator - INFO - Calculating specific futures returns
2025-06-17 02:46:35,361 - analysis.return_rate_calculator - INFO - Calculating signal returns
2025-06-17 02:46:35,361 - analysis.return_rate_calculator - INFO - Processing signal: simple_boll -> position field: boll
2025-06-17 02:46:35,369 - analysis.return_rate_calculator - INFO - Processing signal: simple_rsi -> position field: rsi
2025-06-17 02:46:35,376 - analysis.return_rate_calculator - INFO - Saving return rate data
2025-06-17 02:46:35,376 - analysis.return_rate_calculator - ERROR - Failed to save return rate data: 'types.SimpleNamespace' object has no attribute 'output'
2025-06-17 02:46:35,376 - analysis.comprehensive_analyzer - INFO - Running performance analysis...
2025-06-17 02:46:35,376 - analysis.performance_analyzer - INFO - Analyzing strategy category performance...
2025-06-17 02:46:35,393 - analysis.comprehensive_analyzer - ERROR - Performance analysis failed: cannot reindex on an axis with duplicate labels
2025-06-17 02:46:35,394 - analysis.comprehensive_analyzer - INFO - Running risk analysis...
2025-06-17 02:46:35,394 - analysis.risk_analyzer - INFO - Running comprehensive risk analysis...
2025-06-17 02:46:35,394 - analysis.risk_analyzer - INFO - Calculating historical VaR and ES...
2025-06-17 02:46:35,394 - analysis.risk_analyzer - INFO - Identifying risk concentrations...
2025-06-17 02:46:35,394 - analysis.risk_analyzer - INFO - Calculating correlation matrix...
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Calculating tail risk metrics...
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Calculating stress test scenarios...
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Calculating risk-adjusted returns...
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Validating risk metrics...
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Risk metrics validation completed: 0/0 strategies passed validation
2025-06-17 02:46:35,395 - analysis.risk_analyzer - INFO - Generating risk summary...
2025-06-17 02:46:35,395 - analysis.comprehensive_analyzer - INFO - Running contribution analysis...
2025-06-17 02:46:35,395 - analysis.contribution_analyzer - INFO - Calculating time window contributions with calendar periods...
2025-06-17 02:46:35,398 - analysis.comprehensive_analyzer - ERROR - Contribution analysis failed: 'str' object has no attribute 'strftime'
2025-06-17 02:46:35,398 - analysis.comprehensive_analyzer - ERROR - Key metrics extraction failed: division by zero
2025-06-17 02:46:35,398 - analysis.comprehensive_analyzer - INFO - Comprehensive analysis completed successfully
2025-06-17 02:46:35,398 - __main__ - INFO -   ✅ pnl_analysis: 成功
2025-06-17 02:46:35,398 - __main__ - WARNING -   ❌ position_analysis: 失败
2025-06-17 02:46:35,398 - __main__ - INFO -   ✅ return_rate_analysis: 成功
2025-06-17 02:46:35,398 - __main__ - WARNING -   ❌ performance_analysis: 失败
2025-06-17 02:46:35,398 - __main__ - INFO -   ✅ risk_analysis: 成功
2025-06-17 02:46:35,398 - __main__ - WARNING -   ❌ contribution_analysis: 失败
2025-06-17 02:46:35,398 - __main__ - INFO - ✅ 分析模块测试完成
2025-06-17 02:46:35,398 - __main__ - INFO -   - 成功模块: 3/6
2025-06-17 02:46:35,398 - __main__ - INFO -   - 失败模块: ['position_analysis', 'performance_analysis', 'contribution_analysis']
2025-06-17 02:46:35,398 - __main__ - INFO - ============================================================
2025-06-17 02:46:35,398 - __main__ - INFO - 🎯 系统集成测试总结
2025-06-17 02:46:35,398 - __main__ - INFO -   output_manager: ✅ 通过
2025-06-17 02:46:35,398 - __main__ - INFO -   configuration: ✅ 通过
2025-06-17 02:46:35,398 - __main__ - INFO -   data_loading: ✅ 通过
2025-06-17 02:46:35,398 - __main__ - INFO -   quantstats: ✅ 通过
2025-06-17 02:46:35,398 - __main__ - INFO -   multi_timeframe: ✅ 通过
2025-06-17 02:46:35,398 - __main__ - INFO -   analysis_modules: ❌ 失败
2025-06-17 02:46:35,398 - __main__ - INFO -   report_generation: ❌ 失败
2025-06-17 02:46:35,398 - __main__ - INFO - 
2025-06-17 02:46:35,398 - __main__ - INFO - 总体结果: 5/7 测试通过
2025-06-17 02:46:35,398 - __main__ - WARNING - ⚠️ 2 个测试失败，请检查相关模块。
