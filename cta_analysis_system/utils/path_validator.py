"""
CTA分析系统路径验证工具
"""

import os
import stat
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import logging

logger = logging.getLogger(__name__)


class PathValidator:
    """路径验证器"""
    
    def __init__(self, config):
        """初始化路径验证器"""
        self.config = config
        self.validation_config = config.data.path_validation
        
    def validate_file_path(self, file_path: str, file_type: str = "data") -> Tuple[bool, List[str]]:
        """
        验证文件路径
        
        Args:
            file_path: 文件路径
            file_type: 文件类型（用于错误消息）
            
        Returns:
            Tuple[是否有效, 错误消息列表]
        """
        errors = []
        
        try:
            path_obj = Path(file_path)
            
            # 1. 检查文件是否存在
            if self.validation_config.check_file_exists:
                if not path_obj.exists():
                    errors.append(f"{file_type} file does not exist: {file_path}")
                    return False, errors
                
                if not path_obj.is_file():
                    errors.append(f"Path is not a file: {file_path}")
                    return False, errors
            
            # 2. 检查文件权限
            if self.validation_config.check_file_permissions:
                if not os.access(file_path, os.R_OK):
                    errors.append(f"No read permission for file: {file_path}")
                    return False, errors
            
            # 3. 检查文件大小
            if self.validation_config.max_file_size_mb > 0:
                file_size_mb = path_obj.stat().st_size / (1024 * 1024)
                if file_size_mb > self.validation_config.max_file_size_mb:
                    errors.append(f"File size ({file_size_mb:.2f}MB) exceeds limit "
                                f"({self.validation_config.max_file_size_mb}MB): {file_path}")
                    return False, errors
            
            # 4. 检查文件格式
            file_extension = path_obj.suffix.lower().lstrip('.')
            if file_extension not in self.validation_config.supported_formats:
                errors.append(f"Unsupported file format '{file_extension}'. "
                            f"Supported formats: {self.validation_config.supported_formats}")
                return False, errors
            
            logger.info(f"File path validation passed: {file_path}")
            return True, []
            
        except Exception as e:
            errors.append(f"Error validating file path {file_path}: {str(e)}")
            return False, errors
    
    def validate_directory_path(self, dir_path: str, create_if_missing: bool = False) -> Tuple[bool, List[str]]:
        """
        验证目录路径
        
        Args:
            dir_path: 目录路径
            create_if_missing: 如果目录不存在是否创建
            
        Returns:
            Tuple[是否有效, 错误消息列表]
        """
        errors = []
        
        try:
            path_obj = Path(dir_path)
            
            # 检查目录是否存在
            if not path_obj.exists():
                if create_if_missing:
                    try:
                        path_obj.mkdir(parents=True, exist_ok=True)
                        logger.info(f"Created directory: {dir_path}")
                    except Exception as e:
                        errors.append(f"Failed to create directory {dir_path}: {str(e)}")
                        return False, errors
                else:
                    errors.append(f"Directory does not exist: {dir_path}")
                    return False, errors
            
            # 检查是否为目录
            if not path_obj.is_dir():
                errors.append(f"Path is not a directory: {dir_path}")
                return False, errors
            
            # 检查目录权限
            if self.validation_config.check_file_permissions:
                if not os.access(dir_path, os.R_OK | os.W_OK):
                    errors.append(f"No read/write permission for directory: {dir_path}")
                    return False, errors
            
            logger.info(f"Directory path validation passed: {dir_path}")
            return True, []
            
        except Exception as e:
            errors.append(f"Error validating directory path {dir_path}: {str(e)}")
            return False, errors
    
    def resolve_path(self, path: str, base_path: Optional[str] = None) -> str:
        """
        解析路径，支持相对路径和环境变量
        
        Args:
            path: 原始路径
            base_path: 基础路径（用于相对路径解析）
            
        Returns:
            解析后的绝对路径
        """
        try:
            # 1. 环境变量替换
            resolved_path = os.path.expandvars(path)
            
            # 2. 用户目录替换
            resolved_path = os.path.expanduser(resolved_path)
            
            # 3. 相对路径处理
            if not os.path.isabs(resolved_path):
                if base_path:
                    resolved_path = os.path.join(base_path, resolved_path)
                else:
                    # 使用当前工作目录
                    resolved_path = os.path.abspath(resolved_path)
            
            # 4. 路径标准化
            resolved_path = os.path.normpath(resolved_path)
            
            logger.debug(f"Path resolved: {path} -> {resolved_path}")
            return resolved_path
            
        except Exception as e:
            logger.error(f"Error resolving path {path}: {str(e)}")
            return path
    
    def get_backup_path(self, original_path: str) -> str:
        """
        获取备用文件路径
        
        Args:
            original_path: 原始文件路径
            
        Returns:
            备用文件路径
        """
        try:
            original_file = Path(original_path)
            backup_dir = self.config.data.file_paths.backup_data_dir
            
            # 构建备用文件路径
            backup_path = Path(backup_dir) / original_file.name
            
            return str(backup_path)
            
        except Exception as e:
            logger.error(f"Error getting backup path for {original_path}: {str(e)}")
            return original_path
    
    def validate_all_data_paths(self) -> Dict[str, Any]:
        """
        验证所有数据文件路径
        
        Returns:
            验证结果字典
        """
        validation_results = {
            "overall_valid": True,
            "file_validations": {},
            "directory_validations": {},
            "errors": []
        }
        
        try:
            # 验证主要数据文件
            main_files = {
                "processed_cta_data": self.config.data.file_paths.processed_cta_data,
                "strategy_position": self.config.data.file_paths.strategy_position
            }
            
            for file_type, file_path in main_files.items():
                # 解析文件路径（支持相对路径）
                resolved_path = self.resolve_path(file_path)
                is_valid, errors = self.validate_file_path(resolved_path, file_type)
                
                validation_results["file_validations"][file_type] = {
                    "path": resolved_path,
                    "valid": is_valid,
                    "errors": errors
                }
                
                if not is_valid:
                    validation_results["overall_valid"] = False
                    validation_results["errors"].extend(errors)
            
            # 验证目录路径
            directories = {
                "backup_data_dir": self.config.data.file_paths.backup_data_dir,
                "archive_data_dir": self.config.data.file_paths.archive_data_dir
            }
            
            for dir_type, dir_path in directories.items():
                resolved_path = self.resolve_path(dir_path)
                is_valid, errors = self.validate_directory_path(resolved_path, create_if_missing=True)
                
                validation_results["directory_validations"][dir_type] = {
                    "path": resolved_path,
                    "valid": is_valid,
                    "errors": errors
                }
                
                if not is_valid:
                    # 目录验证失败不影响整体验证（因为是可选的）
                    logger.warning(f"Directory validation failed for {dir_type}: {errors}")
            
            logger.info(f"Path validation completed. Overall valid: {validation_results['overall_valid']}")
            return validation_results
            
        except Exception as e:
            validation_results["overall_valid"] = False
            validation_results["errors"].append(f"Path validation error: {str(e)}")
            logger.error(f"Error during path validation: {str(e)}")
            return validation_results
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            path_obj = Path(file_path)
            
            if not path_obj.exists():
                return {"exists": False}
            
            stat_info = path_obj.stat()
            
            return {
                "exists": True,
                "size_bytes": stat_info.st_size,
                "size_mb": stat_info.st_size / (1024 * 1024),
                "modified_time": stat_info.st_mtime,
                "permissions": oct(stat_info.st_mode)[-3:],
                "is_readable": os.access(file_path, os.R_OK),
                "is_writable": os.access(file_path, os.W_OK),
                "extension": path_obj.suffix.lower().lstrip('.')
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return {"exists": False, "error": str(e)}
