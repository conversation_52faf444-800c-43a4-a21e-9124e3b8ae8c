#!/usr/bin/env python3
"""
输出管理器 (Output Manager)

统一管理系统输出目录结构、文件命名规范和清理策略
"""

import os
import shutil
import logging
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Union
import glob

logger = logging.getLogger(__name__)


class OutputManager:
    """
    输出管理器
    
    负责管理系统的输出目录结构、文件命名和清理策略
    """
    
    def __init__(self, base_output_dir: str = "output"):
        """
        初始化输出管理器
        
        Args:
            base_output_dir: 基础输出目录路径
        """
        self.base_output_dir = Path(base_output_dir)
        
        # 定义标准目录结构
        self.directory_structure = {
            'charts': {
                'performance': 'charts/performance',  # 性能图表
                'risk': 'charts/risk',  # 风险图表
                'contribution': 'charts/contribution',  # 贡献分析图表
                'quantstats': 'charts/quantstats',  # quantstats图表
                'position': 'charts/position',  # 持仓图表
                'pnl': 'charts/pnl',  # 盈亏图表
                'drawdown': 'charts/drawdown'  # 回撤图表
            },
            'reports': {
                'excel': 'reports/excel',  # Excel报告
                'html': 'reports/html',  # HTML报告
                'markdown': 'reports/markdown',  # Markdown报告
                'timeframe_reports': 'reports/timeframe_reports',  # 时间维度报告
                'comprehensive': 'reports/comprehensive'  # 综合报告
            },
            'data_exports': {
                'return_rates': 'data_exports/return_rates',  # 收益率数据
                'analysis_results': 'data_exports/analysis_results',  # 分析结果数据
                'raw_data': 'data_exports/raw_data',  # 原始数据
                'processed_data': 'data_exports/processed_data'  # 处理后数据
            },
            'logs': 'logs',  # 日志文件
            'cache': 'cache',  # 缓存文件
            'temp': 'temp'  # 临时文件
        }
        
        # 文件命名规范
        self.naming_conventions = {
            'timestamp_format': '%Y%m%d_%H%M%S',  # 时间戳格式
            'date_format': '%Y%m%d',  # 日期格式
            'separators': {
                'module': '_',  # 模块分隔符
                'type': '_',  # 类型分隔符
                'timestamp': '_'  # 时间戳分隔符
            }
        }

        # 清理策略配置
        self.cleanup_policies = {
            'enabled': True,  # 启用清理策略
            'max_files_per_directory': 50,  # 每个目录最大文件数
            'max_age_days': 30,  # 文件最大保存天数
            'cleanup_on_startup': True,  # 启动时清理
            'preserve_patterns': ['*_final*', '*_important*', '*_archive*']  # 保留文件模式
        }
        
        # 初始化目录结构
        self.initialize_directories()
    
    def initialize_directories(self) -> bool:
        """
        初始化输出目录结构
        
        Returns:
            初始化是否成功
        """
        try:
            logger.info("正在初始化输出目录结构...")
            
            # 创建基础目录
            self.base_output_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建所有子目录
            all_dirs = []
            for category, subdirs in self.directory_structure.items():
                if isinstance(subdirs, dict):
                    for subdir_name, subdir_path in subdirs.items():
                        all_dirs.append(subdir_path)
                else:
                    all_dirs.append(subdirs)
            
            for dir_path in all_dirs:
                full_path = self.base_output_dir / dir_path
                full_path.mkdir(parents=True, exist_ok=True)
                logger.debug(f"创建目录: {full_path}")
            
            logger.info(f"成功创建 {len(all_dirs)} 个输出目录")
            
            # 执行启动清理
            if self.cleanup_policies['cleanup_on_startup']:
                self.cleanup_old_files()
            
            return True
            
        except Exception as e:
            logger.error(f"初始化输出目录失败: {e}")
            return False
    
    def get_output_path(self, category: str, subcategory: str = None, 
                       filename: str = None, create_dirs: bool = True) -> Path:
        """
        获取标准化的输出路径
        
        Args:
            category: 主要类别 (charts, reports, data_exports)
            subcategory: 子类别 (performance, excel, return_rates等)
            filename: 文件名
            create_dirs: 是否自动创建目录
            
        Returns:
            完整的输出路径
        """
        try:
            # 构建目录路径
            if category in self.directory_structure:
                if subcategory and isinstance(self.directory_structure[category], dict):
                    if subcategory in self.directory_structure[category]:
                        dir_path = self.directory_structure[category][subcategory]
                    else:
                        # 如果子类别不存在，创建新的子目录
                        dir_path = f"{category}/{subcategory}"
                        logger.warning(f"子类别 '{subcategory}' 不在标准结构中，使用: {dir_path}")
                else:
                    dir_path = self.directory_structure[category]
            else:
                # 如果类别不存在，创建新的类别目录
                dir_path = category
                logger.warning(f"类别 '{category}' 不在标准结构中，使用: {dir_path}")
            
            full_dir_path = self.base_output_dir / dir_path
            
            # 创建目录
            if create_dirs:
                full_dir_path.mkdir(parents=True, exist_ok=True)
            
            # 添加文件名
            if filename:
                return full_dir_path / filename
            else:
                return full_dir_path
                
        except Exception as e:
            logger.error(f"获取输出路径失败: {e}")
            return self.base_output_dir / "temp" / (filename or "")
    
    def generate_filename(self, module_name: str, file_type: str, 
                         extension: str, include_timestamp: bool = True,
                         custom_suffix: str = None) -> str:
        """
        生成标准化的文件名
        
        Args:
            module_name: 模块名称 (pnl_analysis, performance等)
            file_type: 文件类型 (report, chart, data等)
            extension: 文件扩展名 (不包含点号)
            include_timestamp: 是否包含时间戳
            custom_suffix: 自定义后缀
            
        Returns:
            标准化的文件名
        """
        try:
            # 构建文件名组件
            components = [module_name, file_type]
            
            # 添加自定义后缀
            if custom_suffix:
                components.append(custom_suffix)
            
            # 添加时间戳
            if include_timestamp:
                timestamp = datetime.now().strftime(self.naming_conventions['timestamp_format'])
                components.append(timestamp)
            
            # 组合文件名
            separator = self.naming_conventions['separators']['module']
            filename = separator.join(components)
            
            # 添加扩展名
            return f"{filename}.{extension}"
            
        except Exception as e:
            logger.error(f"生成文件名失败: {e}")
            # 返回简单的备用文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            return f"{module_name}_{timestamp}.{extension}"
    
    def save_file(self, content: Union[str, bytes], category: str, 
                 subcategory: str, module_name: str, file_type: str,
                 extension: str, custom_suffix: str = None) -> Optional[str]:
        """
        保存文件到标准化路径
        
        Args:
            content: 文件内容
            category: 主要类别
            subcategory: 子类别
            module_name: 模块名称
            file_type: 文件类型
            extension: 文件扩展名
            custom_suffix: 自定义后缀
            
        Returns:
            保存的文件路径，失败时返回None
        """
        try:
            # 生成文件名
            filename = self.generate_filename(
                module_name, file_type, extension, 
                include_timestamp=True, custom_suffix=custom_suffix
            )
            
            # 获取输出路径
            file_path = self.get_output_path(category, subcategory, filename)
            
            # 保存文件
            if isinstance(content, str):
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            else:
                with open(file_path, 'wb') as f:
                    f.write(content)
            
            logger.info(f"文件已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            return None
    
    def cleanup_old_files(self) -> Dict[str, int]:
        """
        清理旧文件
        
        Returns:
            清理统计信息
        """
        cleanup_stats = {
            'directories_processed': 0,
            'files_removed': 0,
            'files_preserved': 0,
            'errors': 0
        }
        
        try:
            if not self.cleanup_policies['enabled']:
                logger.info("文件清理策略已禁用")
                return cleanup_stats
            
            logger.info("开始清理旧文件...")
            
            max_age = timedelta(days=self.cleanup_policies['max_age_days'])
            cutoff_time = datetime.now() - max_age
            max_files = self.cleanup_policies['max_files_per_directory']
            preserve_patterns = self.cleanup_policies['preserve_patterns']
            
            # 遍历所有输出目录
            for root, dirs, files in os.walk(self.base_output_dir):
                cleanup_stats['directories_processed'] += 1
                
                if not files:
                    continue
                
                # 获取文件信息
                file_info = []
                for file in files:
                    file_path = Path(root) / file
                    try:
                        stat = file_path.stat()
                        file_info.append({
                            'path': file_path,
                            'name': file,
                            'mtime': datetime.fromtimestamp(stat.st_mtime),
                            'size': stat.st_size
                        })
                    except Exception as e:
                        logger.warning(f"无法获取文件信息 {file_path}: {e}")
                        cleanup_stats['errors'] += 1
                
                # 按修改时间排序（最新的在前）
                file_info.sort(key=lambda x: x['mtime'], reverse=True)
                
                # 清理策略1: 删除超过最大数量的文件
                if len(file_info) > max_files:
                    files_to_remove = file_info[max_files:]
                    for file_data in files_to_remove:
                        if self._should_preserve_file(file_data['name'], preserve_patterns):
                            cleanup_stats['files_preserved'] += 1
                            continue
                        
                        try:
                            file_data['path'].unlink()
                            cleanup_stats['files_removed'] += 1
                            logger.debug(f"删除文件 (数量限制): {file_data['path']}")
                        except Exception as e:
                            logger.warning(f"删除文件失败: {file_data['path']}: {e}")
                            cleanup_stats['errors'] += 1
                
                # 清理策略2: 删除超过最大年龄的文件
                for file_data in file_info:
                    if file_data['mtime'] < cutoff_time:
                        if self._should_preserve_file(file_data['name'], preserve_patterns):
                            cleanup_stats['files_preserved'] += 1
                            continue
                        
                        try:
                            file_data['path'].unlink()
                            cleanup_stats['files_removed'] += 1
                            logger.debug(f"删除文件 (年龄限制): {file_data['path']}")
                        except Exception as e:
                            logger.warning(f"删除文件失败: {file_data['path']}: {e}")
                            cleanup_stats['errors'] += 1
            
            logger.info(f"文件清理完成: 处理 {cleanup_stats['directories_processed']} 个目录, "
                       f"删除 {cleanup_stats['files_removed']} 个文件, "
                       f"保留 {cleanup_stats['files_preserved']} 个文件, "
                       f"{cleanup_stats['errors']} 个错误")
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"文件清理失败: {e}")
            cleanup_stats['errors'] += 1
            return cleanup_stats
    
    def _should_preserve_file(self, filename: str, preserve_patterns: List[str]) -> bool:
        """
        检查文件是否应该被保留
        
        Args:
            filename: 文件名
            preserve_patterns: 保留模式列表
            
        Returns:
            是否应该保留文件
        """
        import fnmatch
        
        for pattern in preserve_patterns:
            if fnmatch.fnmatch(filename, pattern):
                return True
        return False
    
    def get_directory_info(self) -> Dict[str, Dict[str, Union[int, str]]]:
        """
        获取目录信息统计
        
        Returns:
            目录信息字典
        """
        info = {}
        
        try:
            for category, subdirs in self.directory_structure.items():
                if isinstance(subdirs, dict):
                    for subdir_name, subdir_path in subdirs.items():
                        full_path = self.base_output_dir / subdir_path
                        info[f"{category}/{subdir_name}"] = self._get_path_stats(full_path)
                else:
                    full_path = self.base_output_dir / subdirs
                    info[category] = self._get_path_stats(full_path)
            
            return info
            
        except Exception as e:
            logger.error(f"获取目录信息失败: {e}")
            return {}
    
    def _get_path_stats(self, path: Path) -> Dict[str, Union[int, str]]:
        """获取路径统计信息"""
        try:
            if not path.exists():
                return {'exists': False, 'file_count': 0, 'total_size': 0}
            
            file_count = 0
            total_size = 0
            
            for file_path in path.rglob('*'):
                if file_path.is_file():
                    file_count += 1
                    total_size += file_path.stat().st_size
            
            return {
                'exists': True,
                'file_count': file_count,
                'total_size': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'path': str(path)
            }
            
        except Exception as e:
            logger.warning(f"获取路径统计失败 {path}: {e}")
            return {'exists': False, 'error': str(e)}


# 全局输出管理器实例
output_manager = OutputManager()


def get_output_manager() -> OutputManager:
    """获取全局输出管理器实例"""
    return output_manager
