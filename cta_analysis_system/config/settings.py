"""
CTA分析系统配置管理模块
"""

import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)


@dataclass
class FilePathsConfig:
    """文件路径配置"""
    processed_cta_data: str = "processed_cta_data.csv"
    strategy_position: str = "strategy_position.csv"
    backup_data_dir: str = "data/backup"
    archive_data_dir: str = "data/archive"


@dataclass
class PathValidationConfig:
    """路径验证配置"""
    check_file_exists: bool = True
    check_file_permissions: bool = True
    max_file_size_mb: int = 100
    supported_formats: List[str] = field(default_factory=lambda: ["csv", "xlsx", "parquet"])


@dataclass
class ColumnMappingConfig:
    """字段映射配置"""
    date_column: str = "date"
    pnl_column: str = "pnl"
    strategy_category_column: str = "strategy_category"
    strategy_signal_column: str = "strategy_signal"
    signal_freq_column: str = "signal_freq"
    industry_column: str = "industry"
    symbol_category_column: str = "symbol_category"
    position_column: str = "position"
    position_amount_column: str = "position_amount"
    volume_column: str = "volume"
    turnover_column: str = "turnover"
    commission_column: str = "commission"


@dataclass
class ValidationRulesConfig:
    """数据验证规则配置"""
    processed_cta_data: Dict[str, Any] = field(default_factory=lambda: {
        "required_columns": ["date", "pnl", "strategy_category", "strategy_signal",
                           "signal_freq", "industry", "symbol_category", "position_amount"],
        "column_types": {
            "date": "datetime",
            "pnl": "float",
            "position_amount": "float",
            "strategy_category": "string",
            "industry": "string"
        },
        "value_ranges": {
            "pnl": [-1000000, 1000000],
            "position_amount": [0, 100000]
        }
    })
    strategy_position: Dict[str, Any] = field(default_factory=lambda: {
        "required_columns": ["date", "strategy_category", "position"],
        "column_types": {
            "date": "datetime",
            "position": "float",
            "strategy_category": "string"
        },
        "value_ranges": {
            "position": [0, 10]
        }
    })


@dataclass
class PreprocessingConfig:
    """数据预处理配置"""
    missing_value_handling: Dict[str, str] = field(default_factory=lambda: {
        "numeric_fill_method": "zero",
        "categorical_fill_method": "unknown"
    })
    outlier_handling: Dict[str, Any] = field(default_factory=lambda: {
        "enable_detection": True,
        "detection_method": "iqr",
        "handling_method": "cap"
    })
    type_conversion: Dict[str, Any] = field(default_factory=lambda: {
        "auto_convert": True,
        "date_format": "%Y-%m-%d",
        "float_precision": 2
    })


@dataclass
class DataConfig:
    """数据配置"""
    file_paths: FilePathsConfig = field(default_factory=FilePathsConfig)
    path_validation: PathValidationConfig = field(default_factory=PathValidationConfig)
    column_mapping: ColumnMappingConfig = field(default_factory=ColumnMappingConfig)
    validation_rules: ValidationRulesConfig = field(default_factory=ValidationRulesConfig)
    preprocessing: PreprocessingConfig = field(default_factory=PreprocessingConfig)

    # 向后兼容的属性
    @property
    def processed_cta_data(self) -> str:
        """获取CTA数据文件路径（向后兼容）"""
        return self.file_paths.processed_cta_data

    @property
    def strategy_position(self) -> str:
        """获取持仓数据文件路径（向后兼容）"""
        return self.file_paths.strategy_position

    @property
    def date_column(self) -> str:
        """获取日期字段名（向后兼容）"""
        return self.column_mapping.date_column

    @property
    def pnl_column(self) -> str:
        """获取PnL字段名（向后兼容）"""
        return self.column_mapping.pnl_column

    @property
    def strategy_category_column(self) -> str:
        """获取策略类别字段名（向后兼容）"""
        return self.column_mapping.strategy_category_column

    @property
    def strategy_signal_column(self) -> str:
        """获取策略信号字段名（向后兼容）"""
        return self.column_mapping.strategy_signal_column

    @property
    def signal_freq_column(self) -> str:
        """获取信号频率字段名（向后兼容）"""
        return self.column_mapping.signal_freq_column

    @property
    def industry_column(self) -> str:
        """获取行业字段名（向后兼容）"""
        return self.column_mapping.industry_column

    @property
    def symbol_category_column(self) -> str:
        """获取品种字段名（向后兼容）"""
        return self.column_mapping.symbol_category_column

    @property
    def position_column(self) -> str:
        """获取持仓字段名（向后兼容）"""
        return self.column_mapping.position_column

    @property
    def position_amount_column(self) -> str:
        """获取持仓金额字段名（向后兼容）"""
        return self.column_mapping.position_amount_column

    @property
    def required_columns(self) -> Dict[str, List[str]]:
        """获取必需字段列表（向后兼容）"""
        return {
            "processed_cta_data": self.validation_rules.processed_cta_data.get("required_columns", []),
            "strategy_position": self.validation_rules.strategy_position.get("required_columns", [])
        }


@dataclass
class StrategyConfig:
    """策略配置"""
    categories: List[str] = field(default_factory=lambda: ["option", "trend", "other", "orderflow"])
    exclude_orderflow: bool = False
    trend_subcategories: List[str] = field(default_factory=lambda: ["boll", "rsi"])
    signal_frequencies: List[str] = field(default_factory=lambda: ["1min", "5min", "15min", "30min", "1h", "4h", "1d"])


@dataclass
class RiskMetricsConfig:
    """风险指标配置"""
    var_confidence_levels: List[float] = field(default_factory=lambda: [0.95, 0.99])
    var_time_horizons: List[int] = field(default_factory=lambda: [1, 5, 10])
    rolling_window: int = 250
    risk_free_rate: float = 0.02
    drawdown_threshold: float = 0.05
    validation: Optional['RiskValidationConfig'] = None


@dataclass
class DrawdownAnalysisConfig:
    """回撤分析配置"""
    enabled: bool = True
    identification: Dict[str, Any] = field(default_factory=lambda: {
        "min_drawdown_threshold": 0.01,
        "min_duration_days": 1,
        "method": "peak_valley",
        "rolling_window_size": 20
    })
    time_dimensions: Dict[str, bool] = field(default_factory=lambda: {
        "monthly": True,
        "yearly": True,
        "overall": True
    })
    recovery_analysis: Dict[str, Any] = field(default_factory=lambda: {
        "calculate_recovery": True,
        "recovery_threshold": 0.99,
        "max_recovery_days": 365
    })
    output: Dict[str, Any] = field(default_factory=lambda: {
        "max_drawdowns_display": 10,
        "sort_by": "magnitude",
        "include_unrecovered": True
    })
    visualization: Dict[str, Any] = field(default_factory=lambda: {
        "timeline_chart": True,
        "statistics_chart": True,
        "overlay_on_cumulative": True,
        "colors": {
            "drawdown_area": "#FF6B6B",
            "recovery_area": "#4ECDC4",
            "peak_marker": "#45B7D1",
            "trough_marker": "#96CEB4"
        }
    })


@dataclass
class StrategyPositionMappingConfig:
    """策略-持仓映射配置"""
    mapping_rules: Dict[str, Dict[str, str]] = field(default_factory=lambda: {
        "strategy_level": {
            "other": "other",
            "option": "option",
            "trend": "trend",
            "orderflow": "other"
        },
        "sub_level": {
            "symbol_category": "parent_strategy",
            "future_category": "parent_strategy",
            "industry": "parent_strategy",
            "signal_freq": "parent_strategy"
        }
    })
    default_position: Dict[str, Any] = field(default_factory=lambda: {
        "fallback_strategy": "other",
        "base_capital": 1000000
    })

@dataclass
class RiskValidationConfig:
    """风险指标验证配置"""
    volatility_range: List[float] = field(default_factory=lambda: [0.05, 0.50])
    drawdown_range: List[float] = field(default_factory=lambda: [-0.30, -0.005])
    sharpe_range: List[float] = field(default_factory=lambda: [-2.0, 3.0])
    return_range: List[float] = field(default_factory=lambda: [-0.50, 1.00])
    enable_outlier_detection: bool = True
    outlier_threshold: float = 3.0
    min_data_points: int = 30
    max_missing_ratio: float = 0.20

@dataclass
class TimeWindowsConfig:
    """时间窗口配置"""
    calculation_method: str = "calendar"  # "calendar" 或 "rolling"
    periods: Dict[str, Dict[str, str]] = field(default_factory=lambda: {
        "daily": {"name": "本日", "type": "calendar_day", "description": "当前交易日"},
        "weekly": {"name": "本周", "type": "calendar_week", "description": "从本周一00:00:00到最新数据时间"},
        "monthly": {"name": "本月", "type": "calendar_month", "description": "从本月1日00:00:00到最新数据时间"},
        "quarterly": {"name": "本季度", "type": "calendar_quarter", "description": "从本季度第一天00:00:00到最新数据时间"},
        "yearly": {"name": "本年", "type": "calendar_year", "description": "从本年1月1日00:00:00到最新数据时间"}
    })
    rolling_periods: Dict[str, int] = field(default_factory=lambda: {
        "daily": 1, "weekly": 7, "monthly": 30, "quarterly": 90, "yearly": 252
    })

@dataclass
class PnLAnalysisConfig:
    """盈亏分析配置"""
    primary_classification_fields: List[str] = field(default_factory=lambda: ["strategy_category", "strategy_signal", "signal_freq"])
    pivot_fields: List[str] = field(default_factory=lambda: ["position_direction", "symbol_category", "industry"])
    custom_calculations: Dict[str, Any] = field(default_factory=lambda: {
        "enable_custom_symbol_category": True,
        "enable_custom_industry": True,
        "custom_symbol_groups": {},
        "custom_industry_groups": {}
    })

@dataclass
class ReturnRateCalculationConfig:
    """收益率计算配置"""
    strategy_position_mapping: Dict[str, Any] = field(default_factory=lambda: {
        "strategy_level_mapping": {
            "other": "other",
            "option": "option",
            "trend": "trend",
            "orderflow": "other"
        },
        "stock_index_futures": {
            "symbols": ["IF", "IM", "IC", "IH"],
            "position_field": "fin"
        },
        "treasury_futures": {
            "symbols": ["T", "TS", "TF", "TL"],
            "position_field": "fin"
        },
        "signal_mapping": {
            "simple_boll": "boll",
            "simple_rsi": "rsi"
        }
    })
    validation: Dict[str, Any] = field(default_factory=lambda: {
        "enable_missing_data_warnings": True,
        "missing_data_handling": "skip",
        "min_data_points": 10
    })
    output: Dict[str, Any] = field(default_factory=lambda: {
        "storage_path": "output/cta_analysis/return_rates",
        "use_replacement_mode": True,
        "file_formats": ["csv", "excel"]
    })

@dataclass
class ConcentrationAnalysisConfig:
    """集中度分析配置"""
    analysis_targets: List[str] = field(default_factory=lambda: ["all_positions", "strategy_category"])
    pivot_fields: List[str] = field(default_factory=lambda: ["industry", "symbol_category"])
    calculation: Dict[str, Any] = field(default_factory=lambda: {
        "metrics": ["position_amount", "concentration_ratio"],
        "concentration_method": "absolute_sum"
    })
    output: Dict[str, Any] = field(default_factory=lambda: {
        "fields": ["symbol_category", "position_amount", "concentration_ratio"],
        "sort_by": "concentration_ratio",
        "sort_ascending": False
    })

@dataclass
class ContributionAnalysisConfig:
    """贡献分析配置"""
    analysis_targets: List[str] = field(default_factory=lambda: ["all_positions", "strategy_category"])
    pivot_field: str = "industry"
    calculation: Dict[str, Any] = field(default_factory=lambda: {
        "method": "separate",
        "positive_denominator": "positive_sum",
        "negative_denominator": "negative_sum"
    })
    output: Dict[str, Any] = field(default_factory=lambda: {
        "fields": ["industry", "position_value", "contribution_ratio", "contribution_type"],
        "sort_by": "contribution_ratio",
        "sort_ascending": False
    })

@dataclass
class AnalysisConfig:
    """分析配置"""
    time_windows: TimeWindowsConfig = field(default_factory=TimeWindowsConfig)
    strategy_position_mapping: StrategyPositionMappingConfig = field(default_factory=StrategyPositionMappingConfig)
    risk_metrics: RiskMetricsConfig = field(default_factory=RiskMetricsConfig)
    contribution_analysis: Dict[str, str] = field(default_factory=lambda: {"percentage_method": "modified"})
    drawdown_analysis: DrawdownAnalysisConfig = field(default_factory=DrawdownAnalysisConfig)
    pnl_analysis: PnLAnalysisConfig = field(default_factory=PnLAnalysisConfig)
    return_rate_calculation: ReturnRateCalculationConfig = field(default_factory=ReturnRateCalculationConfig)


@dataclass
class VisualizationConfig:
    """可视化配置"""
    figure_size: List[int] = field(default_factory=lambda: [12, 8])
    dpi: int = 300
    style: str = "seaborn-v0_8"
    colors: Dict[str, str] = field(default_factory=lambda: {
        "profit": "#00AA00",
        "loss": "#FF0000", 
        "neutral": "#0066CC",
        "background": "#FFFFFF"
    })
    color_palettes: Dict[str, str] = field(default_factory=lambda: {
        "primary": "Set3",
        "secondary": "tab20",
        "tertiary": "Paired"
    })
    charts: Dict[str, Dict[str, Any]] = field(default_factory=dict)


@dataclass
class ExcelConfig:
    """Excel配置"""
    worksheets: List[Dict[str, str]] = field(default_factory=list)
    conditional_formatting: Dict[str, Dict[str, str]] = field(default_factory=dict)


@dataclass
class ReportingConfig:
    """报告配置"""
    output_formats: List[str] = field(default_factory=lambda: ["excel", "markdown", "html"])
    excel: ExcelConfig = field(default_factory=ExcelConfig)
    markdown: Dict[str, Any] = field(default_factory=dict)
    recommendations: Dict[str, Dict[str, float]] = field(default_factory=dict)


@dataclass
class StorageConfig:
    """存储配置"""
    base_path: str = "data/output/cta_analysis"
    use_timestamp: bool = True
    timestamp_format: str = "%Y%m%d_%H%M%S"
    backup_enabled: bool = True
    backup_retention_days: int = 30
    compression: str = "gzip"


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_analysis_time: int = 60
    chart_dpi: int = 300
    chart_success_rate_threshold: float = 0.95
    enable_parallel: bool = True
    max_workers: int = 4


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: str = "logs/cta_analysis.log"
    max_file_size: str = "10MB"
    backup_count: int = 5


@dataclass
class DashboardConfig:
    """仪表板配置"""
    enabled: bool = True
    default_time_period: str = "daily"
    metrics: Dict[str, List[str]] = field(default_factory=dict)
    layout: Dict[str, Any] = field(default_factory=dict)
    output: Dict[str, Any] = field(default_factory=dict)
    style: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionAnalysisConfig:
    """持仓分析配置"""
    enabled: bool = True
    netting: Dict[str, Any] = field(default_factory=dict)
    thresholds: Dict[str, Any] = field(default_factory=dict)
    output_format: Dict[str, Any] = field(default_factory=dict)
    concentration_analysis: ConcentrationAnalysisConfig = field(default_factory=ConcentrationAnalysisConfig)
    contribution_analysis: ContributionAnalysisConfig = field(default_factory=ContributionAnalysisConfig)


@dataclass
class OutputConfig:
    """输出配置"""
    base_directory: str = "output/cta_analysis"
    formats: List[str] = field(default_factory=lambda: ["excel", "png", "html", "markdown"])
    file_naming: Dict[str, Any] = field(default_factory=lambda: {
        "timestamp_format": "%Y%m%d_%H%M%S",
        "include_timestamp": True
    })


@dataclass
class CTAConfig:
    """CTA分析系统总配置"""
    data: DataConfig = field(default_factory=DataConfig)
    strategy: StrategyConfig = field(default_factory=StrategyConfig)
    analysis: AnalysisConfig = field(default_factory=AnalysisConfig)
    visualization: VisualizationConfig = field(default_factory=VisualizationConfig)
    reporting: ReportingConfig = field(default_factory=ReportingConfig)
    storage: StorageConfig = field(default_factory=StorageConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    dashboard: DashboardConfig = field(default_factory=DashboardConfig)
    position_analysis: PositionAnalysisConfig = field(default_factory=PositionAnalysisConfig)
    output: OutputConfig = field(default_factory=OutputConfig)


class CTAConfigManager:
    """CTA配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            # 尝试多个可能的配置文件路径
            possible_paths = [
                "config/cta_config.yaml",
                "cta_analysis_system/config/cta_config.yaml",
                Path(__file__).parent / "cta_config.yaml"
            ]

            for path in possible_paths:
                if Path(path).exists():
                    self.config_path = str(path)
                    break
            else:
                self.config_path = "config/cta_config.yaml"
        else:
            self.config_path = config_path

        self.config = self._load_config()
    
    def _load_config(self) -> CTAConfig:
        """加载配置文件"""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
                return self._dict_to_config(config_dict)
            else:
                logger.warning(f"Config file not found: {self.config_path}, using default config")
                return CTAConfig()
        except Exception as e:
            logger.error(f"Failed to load config: {e}, using default config")
            return CTAConfig()
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> CTAConfig:
        """将字典转换为配置对象"""
        try:
            # 数据配置
            data_dict = config_dict.get('data', {})

            # 解析文件路径配置
            file_paths_dict = data_dict.get('file_paths', {})
            file_paths_config = FilePathsConfig(**file_paths_dict)

            # 解析路径验证配置
            path_validation_dict = data_dict.get('path_validation', {})
            path_validation_config = PathValidationConfig(**path_validation_dict)

            # 解析字段映射配置
            column_mapping_dict = data_dict.get('column_mapping', {})
            column_mapping_config = ColumnMappingConfig(**column_mapping_dict)

            # 解析验证规则配置
            validation_rules_dict = data_dict.get('validation_rules', {})
            validation_rules_config = ValidationRulesConfig(**validation_rules_dict)

            # 解析预处理配置
            preprocessing_dict = data_dict.get('preprocessing', {})
            preprocessing_config = PreprocessingConfig(**preprocessing_dict)

            # 创建数据配置对象
            data_config = DataConfig(
                file_paths=file_paths_config,
                path_validation=path_validation_config,
                column_mapping=column_mapping_config,
                validation_rules=validation_rules_config,
                preprocessing=preprocessing_config
            )
            
            # 策略配置
            strategy_config = StrategyConfig(**config_dict.get('strategy', {}))
            
            # 风险指标配置
            risk_metrics_dict = config_dict.get('analysis', {}).get('risk_metrics', {})
            risk_metrics_config = RiskMetricsConfig(**risk_metrics_dict)
            
            # 回撤分析配置
            drawdown_dict = config_dict.get('analysis', {}).get('drawdown_analysis', {})
            drawdown_config = DrawdownAnalysisConfig(
                enabled=drawdown_dict.get('enabled', True),
                identification=drawdown_dict.get('identification', {}),
                time_dimensions=drawdown_dict.get('time_dimensions', {}),
                recovery_analysis=drawdown_dict.get('recovery_analysis', {}),
                output=drawdown_dict.get('output', {}),
                visualization=drawdown_dict.get('visualization', {})
            )

            # 分析配置
            analysis_dict = config_dict.get('analysis', {})

            # 时间窗口配置
            time_windows_dict = analysis_dict.get('time_windows', {})
            time_windows_config = TimeWindowsConfig(
                calculation_method=time_windows_dict.get('calculation_method', 'calendar'),
                periods=time_windows_dict.get('periods', {}),
                rolling_periods=time_windows_dict.get('rolling_periods', {})
            )

            # 策略-持仓映射配置
            strategy_position_mapping_dict = analysis_dict.get('strategy_position_mapping', {})
            strategy_position_mapping_config = StrategyPositionMappingConfig(
                mapping_rules=strategy_position_mapping_dict.get('mapping_rules', {}),
                default_position=strategy_position_mapping_dict.get('default_position', {})
            )

            # 风险验证配置
            risk_validation_dict = risk_metrics_dict.get('validation', {})
            risk_validation_config = RiskValidationConfig(**risk_validation_dict)

            # 盈亏分析配置
            pnl_analysis_dict = analysis_dict.get('pnl_analysis', {})
            pnl_analysis_config = PnLAnalysisConfig(
                primary_classification_fields=pnl_analysis_dict.get('primary_classification_fields', []),
                pivot_fields=pnl_analysis_dict.get('pivot_fields', []),
                custom_calculations=pnl_analysis_dict.get('custom_calculations', {})
            )

            # 收益率计算配置
            return_rate_dict = analysis_dict.get('return_rate_calculation', {})
            return_rate_config = ReturnRateCalculationConfig(
                strategy_position_mapping=return_rate_dict.get('strategy_position_mapping', {}),
                validation=return_rate_dict.get('validation', {}),
                output=return_rate_dict.get('output', {})
            )

            # 更新风险指标配置以包含验证
            risk_metrics_config.validation = risk_validation_config
            analysis_config = AnalysisConfig(
                time_windows=time_windows_config,
                strategy_position_mapping=strategy_position_mapping_config,
                risk_metrics=risk_metrics_config,
                contribution_analysis=analysis_dict.get('contribution_analysis', {}),
                drawdown_analysis=drawdown_config,
                pnl_analysis=pnl_analysis_config,
                return_rate_calculation=return_rate_config
            )
            
            # 可视化配置
            viz_dict = config_dict.get('visualization', {})
            viz_config = VisualizationConfig(
                figure_size=viz_dict.get('figure_size', [12, 8]),
                dpi=viz_dict.get('dpi', 300),
                style=viz_dict.get('style', "seaborn-v0_8"),
                colors=viz_dict.get('colors', {}),
                color_palettes=viz_dict.get('color_palettes', {}),
                charts=viz_dict.get('charts', {})
            )
            
            # Excel配置
            excel_dict = config_dict.get('reporting', {}).get('excel', {})
            excel_config = ExcelConfig(
                worksheets=excel_dict.get('worksheets', []),
                conditional_formatting=excel_dict.get('conditional_formatting', {})
            )
            
            # 报告配置
            reporting_dict = config_dict.get('reporting', {})
            reporting_config = ReportingConfig(
                output_formats=reporting_dict.get('output_formats', []),
                excel=excel_config,
                markdown=reporting_dict.get('markdown', {}),
                recommendations=reporting_dict.get('recommendations', {})
            )
            
            # 存储配置
            storage_config = StorageConfig(**config_dict.get('storage', {}))
            
            # 性能配置
            performance_config = PerformanceConfig(**config_dict.get('performance', {}))
            
            # 日志配置
            logging_config = LoggingConfig(**config_dict.get('logging', {}))

            # 仪表板配置
            dashboard_dict = config_dict.get('dashboard', {})
            dashboard_config = DashboardConfig(
                enabled=dashboard_dict.get('enabled', True),
                default_time_period=dashboard_dict.get('default_time_period', 'daily'),
                metrics=dashboard_dict.get('metrics', {}),
                layout=dashboard_dict.get('layout', {}),
                output=dashboard_dict.get('output', {}),
                style=dashboard_dict.get('style', {})
            )

            # 持仓分析配置
            position_dict = config_dict.get('position_analysis', {})

            # 集中度分析配置
            concentration_dict = position_dict.get('concentration_analysis', {})
            concentration_config = ConcentrationAnalysisConfig(
                analysis_targets=concentration_dict.get('analysis_targets', []),
                pivot_fields=concentration_dict.get('pivot_fields', []),
                calculation=concentration_dict.get('calculation', {}),
                output=concentration_dict.get('output', {})
            )

            # 贡献分析配置
            contribution_dict = position_dict.get('contribution_analysis', {})
            contribution_config = ContributionAnalysisConfig(
                analysis_targets=contribution_dict.get('analysis_targets', []),
                pivot_field=contribution_dict.get('pivot_field', 'industry'),
                calculation=contribution_dict.get('calculation', {}),
                output=contribution_dict.get('output', {})
            )

            position_config = PositionAnalysisConfig(
                enabled=position_dict.get('enabled', True),
                netting=position_dict.get('netting', {}),
                thresholds=position_dict.get('thresholds', {}),
                output_format=position_dict.get('output_format', {}),
                concentration_analysis=concentration_config,
                contribution_analysis=contribution_config
            )

            # 输出配置
            output_dict = config_dict.get('output', {})
            output_config = OutputConfig(
                base_directory=output_dict.get('base_directory', 'output/cta_analysis'),
                formats=output_dict.get('formats', ['excel', 'png', 'html', 'markdown']),
                file_naming=output_dict.get('file_naming', {})
            )

            return CTAConfig(
                data=data_config,
                strategy=strategy_config,
                analysis=analysis_config,
                visualization=viz_config,
                reporting=reporting_config,
                storage=storage_config,
                performance=performance_config,
                logging=logging_config,
                dashboard=dashboard_config,
                position_analysis=position_config,
                output=output_config
            )
            
        except Exception as e:
            logger.error(f"Failed to parse config: {e}")
            return CTAConfig()
    
    def get_config(self) -> CTAConfig:
        """获取配置"""
        return self.config
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def save_config(self, output_path: Optional[str] = None) -> bool:
        """保存配置到文件"""
        try:
            output_file = Path(output_path or self.config_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            config_dict = self._config_to_dict(self.config)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"Config saved to {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save config: {e}")
            return False
    
    def _config_to_dict(self, config: CTAConfig) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        # 这里可以实现配置对象到字典的转换
        # 为简化，暂时返回空字典
        return {}


# 全局配置管理器实例
config_manager = CTAConfigManager()
