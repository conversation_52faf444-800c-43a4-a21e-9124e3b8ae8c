# Reporting Module Configuration
#
# This configuration file contains all settings specific to the reporting module
# including multi-timeframe reporting, output formats, and content customization.

# ================================
# Multi-Timeframe Reporting Configuration
# ================================
timeframe_reporting:
  # Enable multi-timeframe reporting
  enabled: true
  
  # Supported timeframes
  timeframes:
    daily:
      enabled: true
      description: "Daily performance report"
      time_window: "current_day"
      
      # Included analysis modules for daily reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
      
      # Chart types for daily reports
      chart_types:
        - "daily_pnl_summary"
        - "position_concentration"
        - "top_performers"
      
      # Output formats for daily reports
      output_formats:
        - "excel"
        - "html"
      
      # Content depth
      content_depth: "summary"  # "summary", "detailed", "comprehensive"
    
    weekly:
      enabled: true
      description: "Weekly performance report"
      time_window: "current_week"
      
      # Included analysis modules for weekly reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
        - "risk_analysis"
      
      # Chart types for weekly reports
      chart_types:
        - "weekly_pnl_trend"
        - "cumulative_returns"
        - "position_changes"
        - "risk_metrics"
      
      # Output formats for weekly reports
      output_formats:
        - "excel"
        - "html"
        - "markdown"
      
      # Content depth
      content_depth: "detailed"
    
    monthly:
      enabled: true
      description: "Monthly performance report"
      time_window: "current_month"
      
      # Included analysis modules for monthly reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
        - "risk_analysis"
        - "contribution_analysis"
      
      # Chart types for monthly reports
      chart_types:
        - "monthly_pnl_summary"
        - "monthly_heatmap"
        - "drawdown_analysis"
        - "correlation_matrix"
        - "risk_contribution"
      
      # Output formats for monthly reports
      output_formats:
        - "excel"
        - "html"
        - "markdown"
        - "pdf"
      
      # Content depth
      content_depth: "comprehensive"
    
    quarterly:
      enabled: true
      description: "Quarterly performance report"
      time_window: "current_quarter"
      
      # Included analysis modules for quarterly reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
        - "risk_analysis"
        - "contribution_analysis"
        - "drawdown_analysis"
      
      # Chart types for quarterly reports
      chart_types:
        - "quarterly_performance"
        - "rolling_metrics"
        - "sector_analysis"
        - "strategy_comparison"
        - "risk_evolution"
      
      # Output formats for quarterly reports
      output_formats:
        - "excel"
        - "html"
        - "markdown"
        - "pdf"
      
      # Content depth
      content_depth: "comprehensive"
    
    yearly:
      enabled: true
      description: "Annual performance report"
      time_window: "current_year"
      
      # Included analysis modules for yearly reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
        - "risk_analysis"
        - "contribution_analysis"
        - "drawdown_analysis"
      
      # Chart types for yearly reports
      chart_types:
        - "annual_performance"
        - "yearly_comparison"
        - "strategy_evolution"
        - "risk_profile"
        - "attribution_analysis"
      
      # Output formats for yearly reports
      output_formats:
        - "excel"
        - "html"
        - "markdown"
        - "pdf"
      
      # Content depth
      content_depth: "comprehensive"
    
    custom:
      enabled: true
      description: "Custom time period report"
      time_window: "custom"
      
      # Default settings for custom reports
      default_period_days: 30
      max_period_days: 365
      
      # Included analysis modules for custom reports
      included_modules:
        - "pnl_analysis"
        - "position_analysis"
        - "return_rate_analysis"
        - "performance_analysis"
        - "risk_analysis"
        - "contribution_analysis"
      
      # Chart types for custom reports
      chart_types:
        - "custom_period_performance"
        - "period_comparison"
        - "trend_analysis"
      
      # Output formats for custom reports
      output_formats:
        - "excel"
        - "html"
        - "markdown"
      
      # Content depth
      content_depth: "detailed"

# ================================
# Report Content Configuration
# ================================
report_content:
  # Executive summary settings
  executive_summary:
    enabled: true
    include_key_metrics: true
    include_highlights: true
    include_alerts: true
    max_length: 500  # words
  
  # Data tables settings
  data_tables:
    enabled: true
    max_rows_per_table: 50
    include_totals: true
    enable_sorting: true
    enable_filtering: true
  
  # Charts and visualizations
  charts:
    enabled: true
    default_format: "png"
    dpi: 300
    include_captions: true
    enable_interactive: true  # for HTML reports
  
  # Performance metrics
  performance_metrics:
    enabled: true
    include_basic_metrics: true
    include_risk_adjusted_metrics: true
    include_benchmark_comparison: false
  
  # Risk analysis
  risk_analysis:
    enabled: true
    include_var_es: true
    include_stress_tests: true
    include_correlation_analysis: true
  
  # Recommendations
  recommendations:
    enabled: true
    include_risk_warnings: true
    include_allocation_suggestions: true
    include_performance_insights: true

# ================================
# Output Format Configuration
# ================================
output_formats:
  # Excel format settings
  excel:
    enabled: true
    
    # Workbook settings
    workbook:
      include_summary_sheet: true
      include_charts: true
      enable_conditional_formatting: true
    
    # Worksheet configuration
    worksheets:
      summary:
        enabled: true
        name: "执行摘要"
        include_charts: true
      
      pnl_analysis:
        enabled: true
        name: "盈亏分析"
        include_pivot_tables: true
      
      position_analysis:
        enabled: true
        name: "持仓分析"
        include_charts: true
      
      performance_metrics:
        enabled: true
        name: "绩效指标"
        include_charts: true
      
      risk_analysis:
        enabled: true
        name: "风险分析"
        include_charts: true
    
    # Formatting settings
    formatting:
      number_format: "0.00"
      percentage_format: "0.00%"
      currency_format: "¥#,##0.00"
      date_format: "yyyy-mm-dd"
  
  # HTML format settings
  html:
    enabled: true
    
    # Template settings
    template: "professional"  # "basic", "professional", "custom"
    
    # Interactive features
    interactive_features:
      enable_sorting: true
      enable_filtering: true
      enable_drill_down: true
      enable_export: true
    
    # Styling
    styling:
      theme: "light"  # "light", "dark", "custom"
      color_scheme: "blue"  # "blue", "green", "red", "custom"
      font_family: "Arial, sans-serif"
    
    # Chart settings
    charts:
      library: "plotly"  # "plotly", "d3", "chartjs"
      interactive: true
      export_options: ["png", "svg", "pdf"]
  
  # Markdown format settings
  markdown:
    enabled: true
    
    # Content settings
    include_toc: true
    include_charts: true
    chart_format: "png"
    
    # Styling
    style: "github"  # "github", "gitlab", "custom"
  
  # PDF format settings
  pdf:
    enabled: false
    
    # Page settings
    page_size: "A4"
    orientation: "portrait"
    margins: "1in"
    
    # Content settings
    include_charts: true
    chart_dpi: 300

# ================================
# File Management Configuration
# ================================
file_management:
  # Output directory structure
  directory_structure:
    base_dir: "reports"
    create_date_folders: true
    date_folder_format: "%Y%m%d"
    create_timeframe_folders: true
  
  # File naming convention
  file_naming:
    prefix: "cta_analysis_report"
    include_timeframe: true
    include_timestamp: true
    timestamp_format: "%Y%m%d_%H%M%S"
    include_content_suffix: false
  
  # File retention
  retention:
    enabled: true
    max_files_per_timeframe: 30
    max_age_days: 90
    cleanup_on_startup: true
  
  # Compression
  compression:
    enabled: false
    format: "zip"  # "zip", "tar", "gzip"
    include_source_data: false

# ================================
# Quality Control Configuration
# ================================
quality_control:
  # Data validation
  data_validation:
    enabled: true
    check_completeness: true
    min_data_coverage: 0.8
    validate_calculations: true
  
  # Report validation
  report_validation:
    enabled: true
    check_file_integrity: true
    validate_chart_generation: true
    check_table_completeness: true
    validate_links: true
  
  # Error handling
  error_handling:
    continue_on_error: true
    log_errors: true
    include_error_summary: true
    notify_on_critical_errors: false

# ================================
# Performance Configuration
# ================================
performance:
  # Generation settings
  generation:
    max_generation_time: 300  # seconds
    enable_parallel_generation: true
    max_parallel_reports: 3
  
  # Caching
  caching:
    enabled: true
    cache_charts: true
    cache_calculations: true
    cache_expiry: 3600  # seconds
  
  # Memory management
  memory_management:
    max_memory_usage: "2GB"
    enable_garbage_collection: true
    chunk_large_datasets: true

# ================================
# Logging Configuration
# ================================
logging:
  # Log level
  level: "INFO"
  
  # Enable detailed logging
  detailed_logging: true
  
  # Log report generation steps
  log_generation_steps: true
  
  # Log performance metrics
  log_performance_metrics: true
  
  # Log file operations
  log_file_operations: true
