#!/usr/bin/env python3
"""
盈亏分析模块 (PnL Analysis Module)

实现CTA策略的盈亏分析功能，包括：
1. 分类盈亏透视分析
2. 自定义计算功能
3. 多维度盈亏汇总
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class PnLAnalyzer:
    """盈亏分析器"""
    
    def __init__(self, config):
        """
        初始化盈亏分析器

        Args:
            config: 配置对象
        """
        self.config = config

        # Handle both legacy and new configuration formats
        if hasattr(config, 'analysis') and hasattr(config.analysis, 'pnl_analysis'):
            self.pnl_config = config.analysis.pnl_analysis
        else:
            # Fallback to default configuration
            from types import SimpleNamespace
            self.pnl_config = SimpleNamespace()
            self.pnl_config.primary_classification_fields = ["strategy_category", "strategy_signal", "signal_freq"]
            self.pnl_config.pivot_fields = ["position_direction", "symbol_category", "industry"]
            self.pnl_config.custom_calculations = {
                'enable_custom_symbol_category': True,
                'enable_custom_industry': True,
                'custom_symbol_groups': {},
                'custom_industry_groups': {}
            }
        
    def analyze_classified_pnl(self, cta_data: pd.DataFrame, 
                              primary_field: str = None,
                              filter_criteria: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        分类盈亏透视分析
        
        Args:
            cta_data: CTA数据
            primary_field: 一级分类字段
            filter_criteria: 二级筛选条件
            
        Returns:
            分类盈亏分析结果
        """
        logger.info(f"Running classified PnL analysis with primary field: {primary_field}")
        
        try:
            # 使用默认一级分类字段
            if primary_field is None:
                primary_field = self.pnl_config.primary_classification_fields[0]
            
            # 应用筛选条件
            filtered_data = self._apply_filter_criteria(cta_data, filter_criteria)
            
            # 执行透视分析
            pivot_results = {}
            
            for pivot_field in self.pnl_config.pivot_fields:
                if pivot_field in filtered_data.columns:
                    pivot_result = self._calculate_pivot_pnl(
                        filtered_data, primary_field, pivot_field
                    )
                    pivot_results[pivot_field] = pivot_result
            
            # 汇总结果
            summary = self._generate_pnl_summary(filtered_data, primary_field)
            
            return {
                'primary_field': primary_field,
                'filter_criteria': filter_criteria,
                'pivot_results': pivot_results,
                'summary': summary,
                'data_info': {
                    'total_records': len(filtered_data),
                    'date_range': {
                        'start': filtered_data['trade_date'].min(),
                        'end': filtered_data['trade_date'].max()
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"Classified PnL analysis failed: {e}")
            raise
    
    def calculate_custom_pnl(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        自定义计算功能
        
        Args:
            cta_data: CTA数据
            
        Returns:
            自定义计算结果
        """
        logger.info("Running custom PnL calculations")
        
        try:
            results = {}
            
            # 自定义symbol_category计算
            custom_calculations = self.pnl_config.custom_calculations
            if custom_calculations.get('enable_custom_symbol_category', True):
                symbol_results = self._calculate_custom_symbol_groups(cta_data)
                results['custom_symbol_groups'] = symbol_results

            # 自定义industry计算
            if custom_calculations.get('enable_custom_industry', True):
                industry_results = self._calculate_custom_industry_groups(cta_data)
                results['custom_industry_groups'] = industry_results
            
            return results
            
        except Exception as e:
            logger.error(f"Custom PnL calculation failed: {e}")
            raise
    
    def _apply_filter_criteria(self, data: pd.DataFrame, 
                              filter_criteria: Dict[str, Any] = None) -> pd.DataFrame:
        """应用筛选条件"""
        if filter_criteria is None:
            return data.copy()
        
        filtered_data = data.copy()
        
        for field, values in filter_criteria.items():
            if field in filtered_data.columns:
                if isinstance(values, list):
                    filtered_data = filtered_data[filtered_data[field].isin(values)]
                else:
                    filtered_data = filtered_data[filtered_data[field] == values]
        
        logger.info(f"Applied filter criteria: {filter_criteria}, "
                   f"records: {len(data)} -> {len(filtered_data)}")
        
        return filtered_data
    
    def _calculate_pivot_pnl(self, data: pd.DataFrame, 
                            primary_field: str, pivot_field: str) -> Dict[str, Any]:
        """计算透视盈亏"""
        try:
            # 创建透视表
            pivot_table = pd.pivot_table(
                data,
                values='profit_loss_amount',
                index=primary_field,
                columns=pivot_field,
                aggfunc='sum',
                fill_value=0
            )
            
            # 计算汇总统计
            summary_stats = {
                'total_pnl': pivot_table.sum().sum(),
                'positive_pnl': pivot_table[pivot_table > 0].sum().sum(),
                'negative_pnl': pivot_table[pivot_table < 0].sum().sum(),
                'by_primary_field': pivot_table.sum(axis=1).to_dict(),
                'by_pivot_field': pivot_table.sum(axis=0).to_dict()
            }
            
            return {
                'pivot_table': pivot_table.to_dict(),
                'summary_stats': summary_stats,
                'pivot_field': pivot_field,
                'primary_field': primary_field
            }
            
        except Exception as e:
            logger.error(f"Pivot PnL calculation failed for {pivot_field}: {e}")
            return {}
    
    def _calculate_custom_symbol_groups(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算自定义symbol_category组合"""
        try:
            custom_groups = self.pnl_config.custom_calculations.get('custom_symbol_groups', {})
            results = {}
            
            for group_name, symbols in custom_groups.items():
                # 筛选数据
                group_data = data[data['symbol_category'].isin(symbols)]
                
                if not group_data.empty:
                    group_pnl = group_data['profit_loss_amount'].sum()
                    group_count = len(group_data)
                    
                    results[group_name] = {
                        'total_pnl': group_pnl,
                        'record_count': group_count,
                        'symbols': symbols,
                        'avg_pnl_per_record': group_pnl / group_count if group_count > 0 else 0
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"Custom symbol groups calculation failed: {e}")
            return {}
    
    def _calculate_custom_industry_groups(self, data: pd.DataFrame) -> Dict[str, Any]:
        """计算自定义industry组合"""
        try:
            custom_groups = self.pnl_config.custom_calculations.get('custom_industry_groups', {})
            results = {}
            
            for group_name, industries in custom_groups.items():
                # 筛选数据
                group_data = data[data['industry'].isin(industries)]
                
                if not group_data.empty:
                    group_pnl = group_data['profit_loss_amount'].sum()
                    group_count = len(group_data)
                    
                    results[group_name] = {
                        'total_pnl': group_pnl,
                        'record_count': group_count,
                        'industries': industries,
                        'avg_pnl_per_record': group_pnl / group_count if group_count > 0 else 0
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"Custom industry groups calculation failed: {e}")
            return {}
    
    def _generate_pnl_summary(self, data: pd.DataFrame, primary_field: str) -> Dict[str, Any]:
        """生成盈亏汇总"""
        try:
            total_pnl = data['profit_loss_amount'].sum()
            positive_pnl = data[data['profit_loss_amount'] > 0]['profit_loss_amount'].sum()
            negative_pnl = data[data['profit_loss_amount'] < 0]['profit_loss_amount'].sum()
            
            # 按主要字段汇总
            primary_summary = data.groupby(primary_field)['profit_loss_amount'].agg([
                'sum', 'count', 'mean', 'std'
            ]).to_dict('index')
            
            return {
                'total_pnl': total_pnl,
                'positive_pnl': positive_pnl,
                'negative_pnl': negative_pnl,
                'win_rate': len(data[data['profit_loss_amount'] > 0]) / len(data) if len(data) > 0 else 0,
                'primary_field_summary': primary_summary,
                'record_count': len(data)
            }
            
        except Exception as e:
            logger.error(f"PnL summary generation failed: {e}")
            return {}
    
    def run_comprehensive_pnl_analysis(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """
        运行综合盈亏分析
        
        Args:
            cta_data: CTA数据
            
        Returns:
            综合分析结果
        """
        logger.info("Running comprehensive PnL analysis")
        
        try:
            results = {}
            
            # 1. 对每个一级分类字段进行分析
            for primary_field in self.pnl_config.primary_classification_fields:
                if primary_field in cta_data.columns:
                    field_results = self.analyze_classified_pnl(cta_data, primary_field)
                    results[f'classified_by_{primary_field}'] = field_results
            
            # 2. 自定义计算
            custom_results = self.calculate_custom_pnl(cta_data)
            results['custom_calculations'] = custom_results
            
            # 3. 整体汇总
            overall_summary = self._generate_pnl_summary(cta_data, 'strategy_category')
            results['overall_summary'] = overall_summary
            
            logger.info("Comprehensive PnL analysis completed successfully")
            return results
            
        except Exception as e:
            logger.error(f"Comprehensive PnL analysis failed: {e}")
            raise
