"""
CTA策略风险分析模块

Enhanced with Quantstats integration for comprehensive risk metrics.
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
if current_dir.name != 'cta_analysis_system':
    project_root = current_dir.parent
else:
    project_root = current_dir
sys.path.insert(0, str(project_root))


import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from datetime import datetime, timedelta
from scipy import stats
from scipy.optimize import minimize

from config.settings import CTAConfig
from analysis.drawdown_analyzer import DrawdownAnalyzer

# Import quantstats integration
try:
    from .quantstats_integration import QuantstatsIntegrator
    QUANTSTATS_AVAILABLE = True
except ImportError:
    QUANTSTATS_AVAILABLE = False
    logger.warning("Quantstats integration not available for risk analysis")

logger = logging.getLogger(__name__)


class RiskAnalyzer:
    """风险分析器 - Enhanced with Quantstats Integration"""

    def __init__(self, config: CTAConfig):
        self.config = config
        self.drawdown_analyzer = DrawdownAnalyzer(config)

        # Initialize quantstats integrator if available
        if QUANTSTATS_AVAILABLE:
            self.quantstats = QuantstatsIntegrator(config)
            logger.info("Quantstats integration enabled for risk analysis")
        else:
            self.quantstats = None
            logger.warning("Quantstats integration disabled for risk analysis")
    
    def calculate_historical_var_es(self, returns_data: Dict[str, pd.Series], 
                                   rolling_window: int = 250) -> Dict[str, Dict[str, pd.DataFrame]]:
        """计算历史VaR和ES的时间序列"""
        logger.info("Calculating historical VaR and ES...")
        
        results = {}
        confidence_levels = self.config.analysis.risk_metrics.var_confidence_levels
        
        for strategy_name, returns in returns_data.items():
            if len(returns) < rolling_window:
                logger.warning(f"Insufficient data for {strategy_name}: {len(returns)} < {rolling_window}")
                continue
            
            strategy_results = {}
            
            for confidence_level in confidence_levels:
                var_series = []
                es_series = []
                dates = []
                
                for i in range(rolling_window, len(returns)):
                    window_returns = returns.iloc[i-rolling_window:i]
                    
                    # 计算VaR
                    var = np.percentile(window_returns, (1 - confidence_level) * 100)
                    
                    # 计算ES
                    tail_returns = window_returns[window_returns <= var]
                    es = tail_returns.mean() if len(tail_returns) > 0 else var
                    
                    var_series.append(abs(var))
                    es_series.append(abs(es))
                    dates.append(returns.index[i])
                
                strategy_results[f"{int(confidence_level*100)}%"] = pd.DataFrame({
                    'date': dates,
                    'VaR': var_series,
                    'ES': es_series
                }).set_index('date')
            
            results[strategy_name] = strategy_results
        
        return results
    
    def calculate_correlation_matrix(self, returns_data: Dict[str, pd.Series]) -> pd.DataFrame:
        """计算策略间相关性矩阵"""
        logger.info("Calculating correlation matrix...")
        
        # 将所有收益率序列合并到一个DataFrame中
        returns_df = pd.DataFrame(returns_data)
        
        # 计算相关性矩阵
        correlation_matrix = returns_df.corr()
        
        return correlation_matrix
    
    def identify_risk_concentrations(self, returns_data: Dict[str, pd.Series], 
                                   correlation_threshold: float = 0.7) -> Dict[str, Any]:
        """识别风险集中度"""
        logger.info("Identifying risk concentrations...")
        
        correlation_matrix = self.calculate_correlation_matrix(returns_data)
        
        # 找出高相关性策略对
        high_correlation_pairs = []
        n = len(correlation_matrix)
        
        for i in range(n):
            for j in range(i+1, n):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) >= correlation_threshold:
                    high_correlation_pairs.append({
                        'strategy_1': correlation_matrix.index[i],
                        'strategy_2': correlation_matrix.columns[j],
                        'correlation': corr_value
                    })
        
        # 计算组合风险集中度
        weights = {strategy: 1/len(returns_data) for strategy in returns_data.keys()}  # 等权重
        portfolio_variance = self._calculate_portfolio_variance(returns_data, weights, correlation_matrix)
        
        # 计算各策略的风险贡献
        risk_contributions = self._calculate_risk_contributions(returns_data, weights, correlation_matrix)
        
        return {
            "correlation_matrix": correlation_matrix,
            "high_correlation_pairs": high_correlation_pairs,
            "portfolio_variance": portfolio_variance,
            "risk_contributions": risk_contributions,
            "concentration_score": max(risk_contributions.values()) if risk_contributions else 0
        }
    
    def _calculate_portfolio_variance(self, returns_data: Dict[str, pd.Series], 
                                    weights: Dict[str, float], 
                                    correlation_matrix: pd.DataFrame) -> float:
        """计算组合方差"""
        returns_df = pd.DataFrame(returns_data)
        cov_matrix = returns_df.cov()
        
        weight_vector = np.array([weights.get(col, 0) for col in cov_matrix.columns])
        portfolio_var = np.dot(weight_vector.T, np.dot(cov_matrix.values, weight_vector))
        
        return portfolio_var
    
    def _calculate_risk_contributions(self, returns_data: Dict[str, pd.Series], 
                                    weights: Dict[str, float], 
                                    correlation_matrix: pd.DataFrame) -> Dict[str, float]:
        """计算风险贡献度"""
        returns_df = pd.DataFrame(returns_data)
        cov_matrix = returns_df.cov()
        
        weight_vector = np.array([weights.get(col, 0) for col in cov_matrix.columns])
        portfolio_var = np.dot(weight_vector.T, np.dot(cov_matrix.values, weight_vector))
        
        if portfolio_var == 0:
            return {strategy: 0 for strategy in returns_data.keys()}
        
        # 计算边际风险贡献
        marginal_contributions = np.dot(cov_matrix.values, weight_vector)
        
        # 计算风险贡献
        risk_contributions = {}
        for i, strategy in enumerate(cov_matrix.columns):
            contribution = weights.get(strategy, 0) * marginal_contributions[i] / portfolio_var
            risk_contributions[strategy] = contribution
        
        return risk_contributions
    
    def calculate_tail_risk_metrics(self, returns_data: Dict[str, pd.Series]) -> Dict[str, Dict[str, float]]:
        """计算尾部风险指标 - Enhanced with Quantstats"""
        logger.info("Calculating tail risk metrics...")

        results = {}

        for strategy_name, returns in returns_data.items():
            if len(returns) == 0:
                continue

            strategy_metrics = {}

            # Use quantstats for enhanced risk metrics if available
            if self.quantstats:
                try:
                    qs_risk_metrics = self.quantstats.calculate_risk_metrics(returns)
                    strategy_metrics.update(qs_risk_metrics)

                    # Extract specific metrics for backward compatibility
                    strategy_metrics.update({
                        "skewness": qs_risk_metrics.get("skewness", stats.skew(returns.dropna())),
                        "kurtosis": qs_risk_metrics.get("kurtosis", stats.kurtosis(returns.dropna())),
                        "tail_ratio": qs_risk_metrics.get("tail_ratio", 0),
                        "downside_volatility": qs_risk_metrics.get("downside_volatility", 0),
                        "ulcer_index": qs_risk_metrics.get("ulcer_index", 0),
                        "gain_to_pain_ratio": qs_risk_metrics.get("gain_to_pain_ratio", 0)
                    })

                except Exception as e:
                    logger.warning(f"Quantstats risk metrics failed for {strategy_name}: {e}")
                    # Fallback to original calculations
                    strategy_metrics = self._calculate_traditional_tail_metrics(returns)
            else:
                # Fallback to original calculations
                strategy_metrics = self._calculate_traditional_tail_metrics(returns)

            # Always calculate max consecutive losses (custom metric)
            strategy_metrics["max_consecutive_losses"] = self._calculate_max_consecutive_losses(returns)

            results[strategy_name] = strategy_metrics

        return results

    def _calculate_traditional_tail_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate traditional tail risk metrics (fallback method)"""
        # 偏度和峰度
        skewness = stats.skew(returns.dropna())
        kurtosis = stats.kurtosis(returns.dropna())

        # 下行偏差
        downside_returns = returns[returns < 0]
        downside_deviation = downside_returns.std() if len(downside_returns) > 0 else 0

        # 尾部比率（95%分位数 / 5%分位数）
        tail_ratio = abs(np.percentile(returns, 95) / np.percentile(returns, 5)) if np.percentile(returns, 5) != 0 else 0

        return {
            "skewness": skewness,
            "kurtosis": kurtosis,
            "downside_deviation": downside_deviation,
            "tail_ratio": tail_ratio
        }
    
    def _calculate_max_consecutive_losses(self, returns: pd.Series) -> int:
        """计算最大连续亏损天数"""
        if len(returns) == 0:
            return 0
        
        consecutive_losses = 0
        max_consecutive = 0
        
        for ret in returns:
            if ret < 0:
                consecutive_losses += 1
                max_consecutive = max(max_consecutive, consecutive_losses)
            else:
                consecutive_losses = 0
        
        return max_consecutive
    
    def calculate_stress_test_scenarios(self, returns_data: Dict[str, pd.Series]) -> Dict[str, Dict[str, float]]:
        """计算压力测试场景"""
        logger.info("Calculating stress test scenarios...")
        
        results = {}
        
        for strategy_name, returns in returns_data.items():
            if len(returns) == 0:
                continue
            
            # 历史最差情况
            worst_day = returns.min()
            worst_week = returns.rolling(5).sum().min()
            worst_month = returns.rolling(22).sum().min()
            
            # 蒙特卡洛模拟压力测试
            monte_carlo_results = self._monte_carlo_stress_test(returns)
            
            results[strategy_name] = {
                "worst_day": worst_day,
                "worst_week": worst_week,
                "worst_month": worst_month,
                **monte_carlo_results
            }
        
        return results
    
    def _monte_carlo_stress_test(self, returns: pd.Series, n_simulations: int = 1000) -> Dict[str, float]:
        """蒙特卡洛压力测试"""
        if len(returns) == 0:
            return {"mc_var_95": 0, "mc_var_99": 0, "mc_expected_shortfall": 0}
        
        # 使用历史收益率的均值和标准差进行模拟
        mean_return = returns.mean()
        std_return = returns.std()
        
        # 生成模拟收益率
        simulated_returns = np.random.normal(mean_return, std_return, n_simulations)
        
        # 计算VaR
        var_95 = np.percentile(simulated_returns, 5)
        var_99 = np.percentile(simulated_returns, 1)
        
        # 计算期望损失
        tail_returns = simulated_returns[simulated_returns <= var_95]
        expected_shortfall = tail_returns.mean() if len(tail_returns) > 0 else var_95
        
        return {
            "mc_var_95": abs(var_95),
            "mc_var_99": abs(var_99),
            "mc_expected_shortfall": abs(expected_shortfall)
        }
    
    def calculate_risk_adjusted_returns(self, performance_data: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
        """计算风险调整后收益指标"""
        logger.info("Calculating risk-adjusted returns...")
        
        results = {}
        
        for strategy_name, perf_data in performance_data.items():
            if 'returns' not in perf_data or len(perf_data['returns']) == 0:
                continue
            
            returns = perf_data['returns']
            
            # 信息比率
            information_ratio = returns.mean() / returns.std() if returns.std() != 0 else 0
            
            # 卡尔马比率（年化收益率 / 最大回撤）
            annual_return = returns.mean() * 252
            max_drawdown = perf_data.get('max_drawdown', {}).get('max_drawdown', 0)
            calmar_ratio = annual_return / max_drawdown if max_drawdown != 0 else 0
            
            # 收益风险比
            positive_returns = returns[returns > 0]
            negative_returns = returns[returns < 0]
            
            gain_loss_ratio = (positive_returns.mean() / abs(negative_returns.mean())) if len(negative_returns) > 0 and negative_returns.mean() != 0 else 0
            
            # 稳定性指标（收益率标准差的倒数）
            stability_ratio = 1 / returns.std() if returns.std() != 0 else 0
            
            results[strategy_name] = {
                "information_ratio": information_ratio,
                "calmar_ratio": calmar_ratio,
                "gain_loss_ratio": gain_loss_ratio,
                "stability_ratio": stability_ratio
            }
        
        return results
    
    def generate_risk_summary(self, risk_analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成风险分析摘要"""
        logger.info("Generating risk summary...")
        
        summary = {
            "overall_risk_level": "Medium",  # 默认值
            "key_risks": [],
            "recommendations": [],
            "risk_metrics_summary": {}
        }
        
        # 分析相关性风险
        if "risk_concentrations" in risk_analysis_results:
            concentration_data = risk_analysis_results["risk_concentrations"]
            high_corr_pairs = concentration_data.get("high_correlation_pairs", [])
            
            if len(high_corr_pairs) > 0:
                summary["key_risks"].append("High correlation between strategies detected")
                summary["recommendations"].append("Consider diversifying strategy allocation")
            
            concentration_score = concentration_data.get("concentration_score", 0)
            if concentration_score > 0.5:
                summary["key_risks"].append("High risk concentration in single strategy")
                summary["recommendations"].append("Rebalance portfolio to reduce concentration")
        
        # 分析尾部风险
        if "tail_risk_metrics" in risk_analysis_results:
            tail_metrics = risk_analysis_results["tail_risk_metrics"]
            
            for strategy, metrics in tail_metrics.items():
                if metrics.get("max_consecutive_losses", 0) > 5:
                    summary["key_risks"].append(f"High consecutive losses in {strategy}")
                
                if abs(metrics.get("skewness", 0)) > 1:
                    summary["key_risks"].append(f"High skewness in {strategy} returns")
        
        # 整体风险等级评估
        risk_score = len(summary["key_risks"])
        if risk_score == 0:
            summary["overall_risk_level"] = "Low"
        elif risk_score <= 2:
            summary["overall_risk_level"] = "Medium"
        else:
            summary["overall_risk_level"] = "High"
        
        return summary

    def validate_risk_metrics(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """验证风险指标的合理性"""
        logger.info("Validating risk metrics...")

        validation_results = {
            "validation_summary": {
                "total_strategies": 0,
                "valid_strategies": 0,
                "warnings": [],
                "errors": []
            },
            "strategy_validation": {}
        }

        # 获取验证配置
        if hasattr(self.config.analysis, 'risk_metrics') and hasattr(self.config.analysis.risk_metrics, 'validation'):
            validation_config = self.config.analysis.risk_metrics.validation
        else:
            # 使用默认验证范围
            validation_config = type('Config', (), {
                'volatility_range': [0.05, 0.50],
                'drawdown_range': [-0.30, -0.005],
                'sharpe_range': [-2.0, 3.0],
                'return_range': [-0.50, 1.00],
                'min_data_points': 30,
                'max_missing_ratio': 0.20
            })()

        # 验证策略级别指标
        if "strategy_category" in performance_data:
            for strategy, data in performance_data["strategy_category"].items():
                validation_results["validation_summary"]["total_strategies"] += 1
                strategy_validation = self._validate_strategy_metrics(strategy, data, validation_config)
                validation_results["strategy_validation"][strategy] = strategy_validation

                if strategy_validation["is_valid"]:
                    validation_results["validation_summary"]["valid_strategies"] += 1

                # 收集警告和错误
                validation_results["validation_summary"]["warnings"].extend(strategy_validation["warnings"])
                validation_results["validation_summary"]["errors"].extend(strategy_validation["errors"])

        # 计算验证率
        total = validation_results["validation_summary"]["total_strategies"]
        valid = validation_results["validation_summary"]["valid_strategies"]
        validation_results["validation_summary"]["validation_rate"] = valid / total if total > 0 else 0

        # 记录验证结果
        logger.info(f"Risk metrics validation completed: {valid}/{total} strategies passed validation")
        if validation_results["validation_summary"]["warnings"]:
            logger.warning(f"Found {len(validation_results['validation_summary']['warnings'])} warnings")
        if validation_results["validation_summary"]["errors"]:
            logger.error(f"Found {len(validation_results['validation_summary']['errors'])} errors")

        return validation_results

    def _validate_strategy_metrics(self, strategy_name: str, strategy_data: Dict[str, Any], validation_config) -> Dict[str, Any]:
        """验证单个策略的风险指标"""
        validation_result = {
            "is_valid": True,
            "warnings": [],
            "errors": [],
            "metrics_status": {}
        }

        # 检查数据完整性
        if "returns" in strategy_data:
            returns = strategy_data["returns"]
            if len(returns) < validation_config.min_data_points:
                validation_result["errors"].append(f"{strategy_name}: 数据点不足 ({len(returns)} < {validation_config.min_data_points})")
                validation_result["is_valid"] = False

            missing_ratio = returns.isna().sum() / len(returns)
            if missing_ratio > validation_config.max_missing_ratio:
                validation_result["warnings"].append(f"{strategy_name}: 缺失数据比例过高 ({missing_ratio:.2%})")

        # 验证年化波动率
        if "annual_volatility" in strategy_data:
            vol = strategy_data["annual_volatility"]
            if vol < validation_config.volatility_range[0] or vol > validation_config.volatility_range[1]:
                validation_result["warnings"].append(f"{strategy_name}: 年化波动率异常 ({vol:.2%})")
                validation_result["metrics_status"]["volatility"] = "异常"
            else:
                validation_result["metrics_status"]["volatility"] = "正常"

        # 验证最大回撤
        if "max_drawdown" in strategy_data:
            max_dd = strategy_data["max_drawdown"]
            if isinstance(max_dd, dict):
                dd_value = max_dd.get("max_drawdown", 0)
            else:
                dd_value = max_dd

            # 回撤应该是负值或正值小于1
            if dd_value > 1.0:  # 回撤超过100%
                validation_result["errors"].append(f"{strategy_name}: 最大回撤超过100% ({dd_value:.2%})")
                validation_result["is_valid"] = False
                validation_result["metrics_status"]["max_drawdown"] = "错误"
            elif dd_value < validation_config.drawdown_range[0] or dd_value > abs(validation_config.drawdown_range[1]):
                validation_result["warnings"].append(f"{strategy_name}: 最大回撤异常 ({dd_value:.2%})")
                validation_result["metrics_status"]["max_drawdown"] = "异常"
            else:
                validation_result["metrics_status"]["max_drawdown"] = "正常"

        # 验证夏普比率
        if "sharpe_ratio" in strategy_data:
            sharpe = strategy_data["sharpe_ratio"]
            if sharpe < validation_config.sharpe_range[0] or sharpe > validation_config.sharpe_range[1]:
                validation_result["warnings"].append(f"{strategy_name}: 夏普比率异常 ({sharpe:.2f})")
                validation_result["metrics_status"]["sharpe_ratio"] = "异常"
            else:
                validation_result["metrics_status"]["sharpe_ratio"] = "正常"

        # 验证年化收益率
        if "annual_return" in strategy_data:
            annual_ret = strategy_data["annual_return"]
            if annual_ret < validation_config.return_range[0] or annual_ret > validation_config.return_range[1]:
                validation_result["warnings"].append(f"{strategy_name}: 年化收益率异常 ({annual_ret:.2%})")
                validation_result["metrics_status"]["annual_return"] = "异常"
            else:
                validation_result["metrics_status"]["annual_return"] = "正常"

        return validation_result

    def run_comprehensive_risk_analysis(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行综合风险分析，包括回撤区间分析"""
        logger.info("Running comprehensive risk analysis...")

        # 准备收益率数据
        returns_data = self._prepare_returns_data(performance_data)

        # 传统风险分析
        risk_results = {
            "var_es_analysis": self.calculate_historical_var_es(returns_data),
            "risk_concentrations": self.identify_risk_concentrations(returns_data),
            "tail_risk_metrics": self.calculate_tail_risk_metrics(returns_data),
            "stress_test_scenarios": self.calculate_stress_test_scenarios(returns_data),
            "risk_adjusted_returns": self.calculate_risk_adjusted_returns(performance_data)
        }

        # 风险指标验证
        risk_results["risk_validation"] = self.validate_risk_metrics(performance_data)

        # 回撤区间分析
        if hasattr(self.config.analysis, 'drawdown_analysis') and self.config.analysis.drawdown_analysis.enabled:
            logger.info("Adding drawdown analysis...")
            risk_results["drawdown_analysis"] = self.drawdown_analyzer.analyze_drawdown_periods(performance_data)

        # 生成风险摘要
        risk_results["risk_summary"] = self.generate_risk_summary(risk_results)

        return risk_results

    def _prepare_returns_data(self, performance_data: Dict[str, Any]) -> Dict[str, pd.Series]:
        """准备收益率数据"""
        returns_data = {}

        strategy_data = performance_data.get("strategy_category", {})
        for strategy_name, strategy_info in strategy_data.items():
            # 使用已经正确计算的收益率数据
            if "returns" in strategy_info and isinstance(strategy_info["returns"], pd.Series):
                returns_data[strategy_name] = strategy_info["returns"]
            elif "daily_pnl" in strategy_info and isinstance(strategy_info["daily_pnl"], pd.Series):
                # 回退方案：如果没有收益率数据，使用简化计算
                daily_pnl = strategy_info["daily_pnl"]
                base_value = daily_pnl.abs().rolling(window=30).mean().fillna(daily_pnl.abs().mean())
                returns = daily_pnl / base_value
                returns_data[strategy_name] = returns.fillna(0)

        return returns_data
