#!/usr/bin/env python3
"""
CTA策略综合分析器 (Comprehensive Analyzer)

整合所有分析模块，提供统一的分析接口：
1. 盈亏分析
2. 持仓分析
3. 收益率计算
4. 性能/风险/回撤分析
5. 报告生成
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os

from .pnl_analyzer import PnLAnalyzer
from .return_rate_calculator import ReturnRateCalculator
from .position_analyzer import PositionAnalyzer
from .performance_analyzer import StrategyPerformanceAnalyzer
from .risk_analyzer import RiskAnalyzer
from .contribution_analyzer import ContributionAnalyzer

# Import output manager
try:
    from utils.output_manager import get_output_manager
    OUTPUT_MANAGER_AVAILABLE = True
except ImportError:
    OUTPUT_MANAGER_AVAILABLE = False
    logger.warning("输出管理器不可用")

logger = logging.getLogger(__name__)


class ComprehensiveAnalyzer:
    """综合分析器"""
    
    def __init__(self, config):
        """
        初始化综合分析器

        Args:
            config: 配置对象或字典
        """
        self.config = config

        # Handle both legacy config objects and new dict-based configs
        if hasattr(config, 'analysis'):
            # Legacy config object
            config_obj = config
        else:
            # New dict-based config - create a simple object wrapper for backward compatibility
            from types import SimpleNamespace
            config_obj = SimpleNamespace()
            config_obj.analysis = SimpleNamespace()
            config_obj.data = SimpleNamespace()
            config_obj.output = SimpleNamespace()

            # Map dict config to object attributes
            if isinstance(config, dict):
                # Analysis config
                analysis_config = config.get('analysis', {})
                for key, value in analysis_config.items():
                    if isinstance(value, dict):
                        setattr(config_obj.analysis, key, SimpleNamespace(**value))
                    else:
                        setattr(config_obj.analysis, key, value)

                # Data config with column mappings
                data_config = config.get('data', {})
                for key, value in data_config.items():
                    if isinstance(value, dict):
                        setattr(config_obj.data, key, SimpleNamespace(**value))
                    else:
                        setattr(config_obj.data, key, value)

                # Add specific column mappings for backward compatibility
                column_mappings = data_config.get('column_mappings', {})
                if column_mappings:
                    cta_mappings = column_mappings.get('cta_data', {})
                    pos_mappings = column_mappings.get('position_data', {})

                    # Set individual column attributes
                    config_obj.data.date_column = cta_mappings.get('date_column', 'trade_date')
                    config_obj.data.strategy_category_column = cta_mappings.get('strategy_category_column', 'strategy_category')
                    config_obj.data.pnl_column = cta_mappings.get('pnl_column', 'profit_loss_amount')

                # Output config
                output_config = config.get('output', {})
                for key, value in output_config.items():
                    if isinstance(value, dict):
                        setattr(config_obj.output, key, SimpleNamespace(**value))
                    else:
                        setattr(config_obj.output, key, value)

                # Add default analysis configurations if missing
                if not hasattr(config_obj.analysis, 'risk_metrics'):
                    config_obj.analysis.risk_metrics = SimpleNamespace()
                    config_obj.analysis.risk_metrics.var_confidence_levels = [0.95, 0.99]
                    config_obj.analysis.risk_metrics.risk_free_rate = 0.03

        # 初始化各个分析器
        self.pnl_analyzer = PnLAnalyzer(config_obj)
        self.return_calculator = ReturnRateCalculator(config_obj)
        self.position_analyzer = PositionAnalyzer(config_obj)
        self.performance_analyzer = StrategyPerformanceAnalyzer(config_obj)
        self.risk_analyzer = RiskAnalyzer(config_obj)
        self.contribution_analyzer = ContributionAnalyzer(config_obj)

        # 分析结果存储
        self.analysis_results = {}
        self.issues_found = []
        self.resolutions_applied = []
        
    def run_comprehensive_analysis(self, cta_data: pd.DataFrame, 
                                 position_data: pd.DataFrame) -> Dict[str, Any]:
        """
        运行综合分析
        
        Args:
            cta_data: CTA数据
            position_data: 持仓数据
            
        Returns:
            综合分析结果
        """
        logger.info("Starting comprehensive CTA analysis")
        
        try:
            # 1. 盈亏分析
            logger.info("Running PnL analysis...")
            pnl_results = self.pnl_analyzer.run_comprehensive_pnl_analysis(cta_data)
            self.analysis_results['pnl_analysis'] = pnl_results
            
            # 2. 持仓分析
            logger.info("Running position analysis...")
            position_results = self._run_position_analysis(cta_data, position_data)
            self.analysis_results['position_analysis'] = position_results
            
            # 3. 收益率计算
            logger.info("Running return rate calculation...")
            return_results = self._run_return_rate_calculation(cta_data, position_data)
            self.analysis_results['return_rate_analysis'] = return_results
            
            # 4. 性能分析
            logger.info("Running performance analysis...")
            performance_results = self._run_performance_analysis(cta_data, position_data)
            self.analysis_results['performance_analysis'] = performance_results
            
            # 5. 风险分析
            logger.info("Running risk analysis...")
            risk_results = self._run_risk_analysis()
            self.analysis_results['risk_analysis'] = risk_results
            
            # 6. 贡献分析
            logger.info("Running contribution analysis...")
            contribution_results = self._run_contribution_analysis(cta_data)
            self.analysis_results['contribution_analysis'] = contribution_results
            
            # 7. 生成综合摘要
            summary = self._generate_comprehensive_summary()
            self.analysis_results['comprehensive_summary'] = summary
            
            logger.info("Comprehensive analysis completed successfully")
            return self.analysis_results
            
        except Exception as e:
            logger.error(f"Comprehensive analysis failed: {e}")
            self.issues_found.append(f"Comprehensive analysis error: {e}")
            raise
    
    def _run_position_analysis(self, cta_data: pd.DataFrame, 
                             position_data: pd.DataFrame) -> Dict[str, Any]:
        """运行持仓分析"""
        try:
            results = {}
            
            # 当前持仓分析
            current_positions = self.position_analyzer.analyze_current_positions(cta_data, position_data)
            results['current_positions'] = current_positions
            
            # 集中度分析
            concentration_analysis = self.position_analyzer.analyze_concentration(cta_data)
            results['concentration_analysis'] = concentration_analysis
            
            # 持仓贡献分析
            contribution_analysis = self.position_analyzer.analyze_position_contribution(cta_data)
            results['contribution_analysis'] = contribution_analysis
            
            return results
            
        except Exception as e:
            logger.error(f"Position analysis failed: {e}")
            self.issues_found.append(f"Position analysis error: {e}")
            return {}
    
    def _run_return_rate_calculation(self, cta_data: pd.DataFrame, 
                                   position_data: pd.DataFrame) -> Dict[str, Any]:
        """运行收益率计算"""
        try:
            results = {}
            
            # 策略类别收益率
            strategy_returns = self.return_calculator.calculate_strategy_category_returns(
                cta_data, position_data
            )
            results['strategy_category_returns'] = strategy_returns
            
            # 特定期货收益率
            futures_returns = self.return_calculator.calculate_specific_futures_returns(
                cta_data, position_data
            )
            results['specific_futures_returns'] = futures_returns
            
            # 策略信号收益率
            signal_returns = self.return_calculator.calculate_signal_returns(
                cta_data, position_data
            )
            results['signal_returns'] = signal_returns
            
            # 保存收益率数据
            saved_files = self.return_calculator.save_return_data(results)
            results['saved_files'] = saved_files
            
            return results
            
        except Exception as e:
            logger.error(f"Return rate calculation failed: {e}")
            self.issues_found.append(f"Return rate calculation error: {e}")
            return {}
    
    def _run_performance_analysis(self, cta_data: pd.DataFrame, 
                                position_data: pd.DataFrame) -> Dict[str, Any]:
        """运行性能分析"""
        try:
            # 策略类别性能分析
            strategy_results = self.performance_analyzer.analyze_strategy_category_performance(
                cta_data, position_data
            )
            
            # 时间窗口性能分析
            time_window_results = self.performance_analyzer.calculate_time_window_performance(cta_data)
            
            return {
                'strategy_category': strategy_results,
                'time_window_performance': time_window_results
            }
            
        except Exception as e:
            logger.error(f"Performance analysis failed: {e}")
            self.issues_found.append(f"Performance analysis error: {e}")
            return {}
    
    def _run_risk_analysis(self) -> Dict[str, Any]:
        """运行风险分析"""
        try:
            # 检查是否有性能分析结果
            if 'performance_analysis' not in self.analysis_results:
                logger.warning("No performance results available for risk analysis")
                return {}
            
            # 综合风险分析
            risk_results = self.risk_analyzer.run_comprehensive_risk_analysis(
                self.analysis_results['performance_analysis']
            )
            
            return risk_results
            
        except Exception as e:
            logger.error(f"Risk analysis failed: {e}")
            self.issues_found.append(f"Risk analysis error: {e}")
            return {}
    
    def _run_contribution_analysis(self, cta_data: pd.DataFrame) -> Dict[str, Any]:
        """运行贡献分析"""
        try:
            # 时间窗口贡献分析
            contribution_results = self.contribution_analyzer.calculate_time_window_contributions(cta_data)
            
            return contribution_results
            
        except Exception as e:
            logger.error(f"Contribution analysis failed: {e}")
            self.issues_found.append(f"Contribution analysis error: {e}")
            return {}
    
    def _generate_comprehensive_summary(self) -> Dict[str, Any]:
        """生成综合摘要"""
        try:
            summary = {
                'analysis_timestamp': datetime.now().isoformat(),
                'modules_completed': [],
                'modules_failed': [],
                'key_metrics': {},
                'data_quality': {},
                'recommendations': []
            }
            
            # 检查各模块完成情况
            expected_modules = [
                'pnl_analysis', 'position_analysis', 'return_rate_analysis',
                'performance_analysis', 'risk_analysis', 'contribution_analysis'
            ]
            
            for module in expected_modules:
                if module in self.analysis_results and self.analysis_results[module]:
                    summary['modules_completed'].append(module)
                else:
                    summary['modules_failed'].append(module)
            
            # 提取关键指标
            summary['key_metrics'] = self._extract_key_metrics()
            
            # 数据质量评估
            summary['data_quality'] = self._assess_data_quality()
            
            # 生成建议
            summary['recommendations'] = self._generate_recommendations()
            
            return summary
            
        except Exception as e:
            logger.error(f"Summary generation failed: {e}")
            return {}
    
    def _extract_key_metrics(self) -> Dict[str, Any]:
        """提取关键指标"""
        key_metrics = {}
        
        try:
            # 从盈亏分析提取
            if 'pnl_analysis' in self.analysis_results:
                pnl_summary = self.analysis_results['pnl_analysis'].get('overall_summary', {})
                key_metrics['total_pnl'] = pnl_summary.get('total_pnl', 0)
                key_metrics['win_rate'] = pnl_summary.get('win_rate', 0)
            
            # 从持仓分析提取
            if 'position_analysis' in self.analysis_results:
                position_summary = self.analysis_results['position_analysis'].get('current_positions', {}).get('summary', {})
                key_metrics['total_position'] = position_summary.get('total_position', 0)
                key_metrics['strategy_count'] = position_summary.get('strategy_count', 0)
            
            # 从风险分析提取
            if 'risk_analysis' in self.analysis_results:
                risk_validation = self.analysis_results['risk_analysis'].get('risk_validation', {})
                if 'validation_summary' in risk_validation:
                    validation_summary = risk_validation['validation_summary']
                    key_metrics['risk_validation_rate'] = (
                        validation_summary.get('valid_strategies', 0) / 
                        validation_summary.get('total_strategies', 1)
                    )
            
        except Exception as e:
            logger.error(f"Key metrics extraction failed: {e}")
        
        return key_metrics
    
    def _assess_data_quality(self) -> Dict[str, Any]:
        """评估数据质量"""
        quality_assessment = {
            'overall_score': 0.0,
            'issues': [],
            'warnings': []
        }
        
        try:
            # 检查收益率计算的验证结果
            if 'return_rate_analysis' in self.analysis_results:
                return_results = self.analysis_results['return_rate_analysis']
                
                for category, results in return_results.items():
                    if isinstance(results, dict):
                        for strategy, strategy_data in results.items():
                            if 'validation' in strategy_data:
                                validation = strategy_data['validation']
                                if not validation.get('valid', True):
                                    quality_assessment['issues'].extend(validation.get('errors', []))
                                quality_assessment['warnings'].extend(validation.get('warnings', []))
            
            # 计算总体质量分数
            total_issues = len(quality_assessment['issues'])
            total_warnings = len(quality_assessment['warnings'])
            
            # 简单的质量评分逻辑
            if total_issues == 0 and total_warnings == 0:
                quality_assessment['overall_score'] = 1.0
            elif total_issues == 0:
                quality_assessment['overall_score'] = max(0.7, 1.0 - total_warnings * 0.1)
            else:
                quality_assessment['overall_score'] = max(0.3, 0.7 - total_issues * 0.2)
            
        except Exception as e:
            logger.error(f"Data quality assessment failed: {e}")
            quality_assessment['issues'].append(f"Quality assessment error: {e}")
        
        return quality_assessment
    
    def _generate_recommendations(self) -> List[str]:
        """生成建议"""
        recommendations = []
        
        try:
            # 基于数据质量生成建议
            data_quality = self._assess_data_quality()
            if data_quality['overall_score'] < 0.8:
                recommendations.append("建议检查数据质量，存在较多警告或错误")
            
            # 基于风险分析生成建议
            if 'risk_analysis' in self.analysis_results:
                risk_results = self.analysis_results['risk_analysis']
                if 'risk_validation' in risk_results:
                    validation_summary = risk_results['risk_validation'].get('validation_summary', {})
                    if validation_summary.get('errors', []):
                        recommendations.append("建议关注风险指标异常的策略")
            
            # 基于持仓集中度生成建议
            if 'position_analysis' in self.analysis_results:
                concentration = self.analysis_results['position_analysis'].get('concentration_analysis', {})
                # 这里可以添加基于集中度的建议逻辑
            
        except Exception as e:
            logger.error(f"Recommendations generation failed: {e}")
            recommendations.append(f"建议生成过程出现错误: {e}")
        
        return recommendations
    
    def get_analysis_results(self) -> Dict[str, Any]:
        """获取分析结果"""
        return self.analysis_results.copy()
    
    def get_issues_found(self) -> List[str]:
        """获取发现的问题"""
        return self.issues_found.copy()
    
    def get_resolutions_applied(self) -> List[str]:
        """获取应用的解决方案"""
        return self.resolutions_applied.copy()
