# CTA策略分析系统配置文件
#
# 配置说明：
# - 所有路径均为相对于项目根目录的相对路径
# - 数据文件路径可根据实际部署环境进行调整
# - 配置项支持环境变量替换（格式：${ENV_VAR}）

# ================================
# 数据配置 (Data Configuration)
# ================================
data:
  # 数据文件路径配置
  # 说明：所有路径均相对于项目根目录，支持以下格式：
  #   - 相对路径：data/processed_cta_data.csv
  #   - 子目录路径：cta_analysis_system/processed_cta_data.csv
  #   - 环境变量：${CTA_DATA_PATH}/processed_cta_data.csv
  file_paths:
    # CTA策略数据文件路径
    # 包含策略表现、PnL、行业分类等核心数据
    processed_cta_data: "input/processed_cta_data.csv"

    # 策略持仓数据文件路径
    # 包含各策略的持仓权重和配置信息
    strategy_position: "input/strategy_position.csv"

    # 备用数据路径（可选）
    # 当主数据文件不可用时的备用数据源
    backup_data_dir: "data/backup"

    # 历史数据存档路径（可选）
    # 用于存储历史分析数据
    archive_data_dir: "data/archive"

  # 路径验证配置
  path_validation:
    # 是否启用路径存在性检查
    check_file_exists: true

    # 是否启用路径权限检查
    check_file_permissions: true

    # 文件大小限制（MB，0表示无限制）
    max_file_size_mb: 100

    # 支持的文件格式
    supported_formats: ["csv", "xlsx", "parquet"]

  # 数据字段映射配置
  # 说明：定义数据文件中各字段的标准名称映射
  column_mapping:
    # 基础字段
    date_column: "trade_date"                       # 日期字段
    pnl_column: "profit_loss_amount"               # 损益字段

    # 策略分类字段
    strategy_category_column: "strategy_category"  # 策略类别
    strategy_signal_column: "strategy_signal"      # 策略信号
    signal_freq_column: "signal_freq"             # 信号频率

    # 市场分类字段
    industry_column: "industry"                    # 行业分类
    symbol_category_column: "symbol_category"      # 品种分类

    # 持仓相关字段
    position_column: "position"                    # 持仓权重
    position_amount_column: "position_amount"      # 持仓金额（万元）

    # 扩展字段（可选）
    volume_column: "volume"                        # 成交量
    turnover_column: "turnover"                    # 成交额
    commission_column: "commission"                # 手续费

  # 数据验证规则
  # 说明：定义各数据文件必须包含的字段和验证规则
  validation_rules:
    # CTA数据文件必需字段
    processed_cta_data:
      required_columns:
        - "trade_date"
        - "profit_loss_amount"
        - "strategy_category"
        - "strategy_signal"
        - "signal_freq"
        - "industry"
        - "symbol_category"
        - "position_amount"

      # 字段类型验证
      column_types:
        trade_date: "datetime"
        profit_loss_amount: "float"
        position_amount: "float"
        strategy_category: "string"
        industry: "string"

      # 数值范围验证
      value_ranges:
        profit_loss_amount: [-1000000, 1000000]        # PnL范围（元）
        position_amount: [0, 100000]    # 持仓金额范围（万元）

    # 持仓数据文件必需字段
    strategy_position:
      required_columns:
        - "date"
        - "other"
        - "option"
        - "trend"

      # 字段类型验证
      column_types:
        date: "datetime"
        other: "float"
        option: "float"
        trend: "float"

      # 数值范围验证
      value_ranges:
        other: [0, 10]               # 持仓权重范围
        option: [0, 10]
        trend: [0, 10]

  # 数据预处理配置
  preprocessing:
    # 缺失值处理
    missing_value_handling:
      # 数值字段缺失值填充方法：'mean', 'median', 'zero', 'forward_fill'
      numeric_fill_method: "zero"

      # 分类字段缺失值填充方法：'mode', 'unknown', 'forward_fill'
      categorical_fill_method: "unknown"

    # 异常值处理
    outlier_handling:
      # 是否启用异常值检测
      enable_detection: true

      # 异常值检测方法：'iqr', 'zscore', 'isolation_forest'
      detection_method: "iqr"

      # 异常值处理方法：'remove', 'cap', 'transform'
      handling_method: "cap"

    # 数据类型转换
    type_conversion:
      # 是否自动转换数据类型
      auto_convert: true

      # 日期格式
      date_format: "%Y-%m-%d"

      # 数值精度
      float_precision: 2

# 策略配置
strategy:
  # 策略类别配置
  categories:
    - "option"
    - "trend" 
    - "other"
    - "orderflow"
  
  # 是否排除orderflow策略
  exclude_orderflow: false
  
  # trend策略子分类
  trend_subcategories:
    - "boll"
    - "rsi"
  
  # 信号频率类别
  signal_frequencies:
    - "1min"
    - "5min"
    - "15min"
    - "30min"
    - "1h"
    - "4h"
    - "1d"

# 分析配置
analysis:
  # 时间窗口配置 - 使用日历期间而非滚动期间
  time_windows:
    # 时间窗口类型：calendar（日历期间）或 rolling（滚动期间）
    calculation_method: "calendar"

    # 各时间窗口定义
    periods:
      daily:
        name: "本日"
        type: "calendar_day"
        description: "当前交易日"
      weekly:
        name: "本周"
        type: "calendar_week"
        description: "从本周一00:00:00到最新数据时间"
      monthly:
        name: "本月"
        type: "calendar_month"
        description: "从本月1日00:00:00到最新数据时间"
      quarterly:
        name: "本季度"
        type: "calendar_quarter"
        description: "从本季度第一天00:00:00到最新数据时间"
      yearly:
        name: "本年"
        type: "calendar_year"
        description: "从本年1月1日00:00:00到最新数据时间"

    # 备用滚动期间配置（向后兼容）
    rolling_periods:
      daily: 1        # 本日
      weekly: 7       # 本周
      monthly: 30     # 本月
      quarterly: 90   # 本季度
      yearly: 252     # 年度（交易日）

  # 策略-持仓映射配置
  strategy_position_mapping:
    # 策略与持仓数据的映射关系
    # 用于收益率计算时确定正确的持仓基准
    mapping_rules:
      # 策略级别收益率计算：使用对应策略的持仓金额作为分母
      strategy_level:
        other: "other"           # other策略对应other持仓
        option: "option"         # option策略对应option持仓
        trend: "trend"           # trend策略对应trend持仓
        orderflow: "other"       # orderflow策略归类到other持仓

      # 子级别分析收益率计算：使用父策略类别的持仓金额作为分母
      sub_level:
        symbol_category: "parent_strategy"    # 品种分类分析使用父策略持仓
        future_category: "parent_strategy"    # 期货分类分析使用父策略持仓
        industry: "parent_strategy"           # 行业分析使用父策略持仓
        signal_freq: "parent_strategy"        # 信号频率分析使用父策略持仓

    # 默认持仓配置
    default_position:
      fallback_strategy: "other"              # 当找不到对应策略时的备用策略
      base_capital: 150000000                   # 基础资金（元），用于无持仓数据时的计算

  # 风险指标配置
  risk_metrics:
    # VaR和ES配置
    var_confidence_levels: [0.95, 0.99]  # 置信水平
    var_time_horizons: [1, 5, 10]        # 时间周期（天）
    rolling_window: 250                   # 滚动窗口（交易日）

    # 夏普比率配置
    risk_free_rate: 0.02                 # 无风险利率

    # 最大回撤配置
    drawdown_threshold: 0.05             # 回撤阈值

    # 风险指标验证配置
    validation:
      # 合理范围检查
      volatility_range: [0.05, 0.50]     # 年化波动率合理范围 (5%-50%)
      drawdown_range: [-0.30, -0.005]    # 最大回撤合理范围 (-30%到-0.5%)
      sharpe_range: [-2.0, 3.0]          # 夏普比率合理范围 (-2到+3)
      return_range: [-0.50, 1.00]        # 年化收益率合理范围 (-50%到+100%)

      # 异常值处理
      enable_outlier_detection: true      # 启用异常值检测
      outlier_threshold: 3.0              # 异常值阈值（标准差倍数）

      # 数据完整性检查
      min_data_points: 30                 # 最少数据点数量
      max_missing_ratio: 0.20             # 最大缺失数据比例
  
  # 盈亏分析配置
  pnl_analysis:
    # 一级分类字段配置
    primary_classification_fields:
      - "strategy_category"
      - "strategy_signal"
      - "signal_freq"

    # 透视字段配置
    pivot_fields:
      - "position_direction"
      - "symbol_category"
      - "industry"

    # 自定义计算配置
    custom_calculations:
      # 是否启用自定义symbol_category计算
      enable_custom_symbol_category: true
      # 是否启用自定义industry计算
      enable_custom_industry: true

      # 自定义symbol_category组合
      custom_symbol_groups:
        "金属类":
          - "cu"
          - "al"
          - "zn"
          - "pb"
        "农产品类":
          - "a"
          - "c"
          - "m"
          - "y"
        "化工类":
          - "eb"
          - "eg"
          - "pp"
          - "ta"

      # 自定义industry组合
      custom_industry_groups:
        "有色金属":
          - "有色金属"
          - "贵金属"
        "农林牧渔":
          - "农产品"
          - "饲料"

  # 收益率计算配置
  return_rate_calculation:
    # 策略-持仓映射配置（扩展版）
    strategy_position_mapping:
      # 策略类别映射
      strategy_level_mapping:
        "other": "other"
        "option": "option"
        "trend": "trend"
        "orderflow": "other"  # orderflow归类到other

      # 特定品种映射（股指期货）
      stock_index_futures:
        symbols: ["IF", "IM", "IC", "IH"]
        position_field: "fin"

      # 特定品种映射（国债期货）
      treasury_futures:
        symbols: ["T", "TS", "TF", "TL"]
        position_field: "fin"

      # 策略信号映射
      signal_mapping:
        "simple_boll": "boll"
        "simple_rsi": "rsi"

    # 数据验证配置
    validation:
      # 是否启用缺失数据提示
      enable_missing_data_warnings: true
      # 缺失数据处理方式：'skip', 'zero', 'interpolate'
      missing_data_handling: "skip"
      # 最小数据点要求
      min_data_points: 10

    # 输出配置
    output:
      # 收益率数据存储路径
      storage_path: "output/cta_analysis/return_rates"
      # 是否使用替换模式
      use_replacement_mode: true
      # 文件格式
      file_formats: ["csv", "excel"]

  # 收益贡献分析配置
  contribution_analysis:
    # 百分比计算方法
    percentage_method: "modified"  # "standard" 或 "modified"
    # modified方法：总体盈利时用Total_Gain做分母，总体亏损时用Total_Loss做分母，但保持原始符号

  # 回撤区间分析配置
  drawdown_analysis:
    # 启用回撤区间分析
    enabled: true

    # 回撤识别参数
    identification:
      # 最小回撤阈值（百分比）
      min_drawdown_threshold: 0.01  # 1%

      # 最短持续天数
      min_duration_days: 1

      # 回撤定义方法：'peak_valley' 或 'rolling_window'
      method: "peak_valley"

      # 移动窗口大小（仅当method为rolling_window时使用）
      rolling_window_size: 20

    # 时间维度分析
    time_dimensions:
      # 是否进行月度回撤分析
      monthly: true

      # 是否进行年度回撤分析
      yearly: true

      # 是否进行整体回撤分析
      overall: true

    # 恢复时间分析
    recovery_analysis:
      # 是否计算恢复时间
      calculate_recovery: true

      # 恢复定义阈值（回到峰值的百分比）
      recovery_threshold: 0.99  # 99%

      # 最大等待恢复天数（超过此天数视为未恢复）
      max_recovery_days: 365

    # 输出配置
    output:
      # 最大显示回撤数量
      max_drawdowns_display: 10

      # 排序方式：'magnitude' 或 'duration'
      sort_by: "magnitude"

      # 是否包含未恢复的回撤
      include_unrecovered: true

    # 可视化配置
    visualization:
      # 是否生成回撤时间线图
      timeline_chart: true

      # 是否生成回撤统计图
      statistics_chart: true

      # 是否在累积收益图上叠加回撤区域
      overlay_on_cumulative: true

      # 回撤区域颜色配置
      colors:
        drawdown_area: "#FF6B6B"      # 回撤区域颜色
        recovery_area: "#4ECDC4"      # 恢复区域颜色
        peak_marker: "#45B7D1"        # 峰值标记颜色
        trough_marker: "#96CEB4"      # 谷值标记颜色

# 可视化配置
visualization:
  # 基础设置
  figure_size: [12, 8]
  dpi: 300
  style: "seaborn-v0_8"
  
  # 颜色配置（高对比度语义色彩）
  colors:
    profit: "#00AA00"      # 绿色 - 盈利
    loss: "#FF0000"        # 红色 - 亏损
    neutral: "#0066CC"     # 蓝色 - 中性
    background: "#FFFFFF"   # 白色背景
  
  # 调色板配置
  color_palettes:
    primary: "Set3"
    secondary: "tab20"
    tertiary: "Paired"
  
  # 图表配置
  charts:
    # 累积收益曲线
    cumulative_returns:
      show_drawdown: true
      show_benchmark: false
      
    # 收益分布
    return_distribution:
      bins: 50
      show_normal_fit: true
      
    # 瀑布图
    waterfall:
      show_total: true
      sort_by_contribution: true
      
    # 散点图
    scatter:
      x_metric: "annual_volatility"
      y_metric: "annual_return"
      size_metric: "sharpe_ratio"
      
    # 热力图
    heatmap:
      annot: true
      cmap: "RdYlGn"
      center: 0

# 报告配置
reporting:
  # 输出格式
  output_formats: ["excel", "markdown", "html"]

  # 时间维度配置
  time_dimensions:
    # 支持的时间维度
    supported_periods:
      - "daily"      # 本日
      - "weekly"     # 本周
      - "monthly"    # 本月
      - "quarterly"  # 本季度
      - "yearly"     # 本年
      - "custom"     # 自定义时间段

    # 默认时间维度
    default_periods: ["daily", "weekly", "monthly", "yearly"]

    # 自定义时间段配置
    custom_period:
      # 是否启用自定义时间段
      enabled: true
      # 默认自定义时间段（天数）
      default_days: 30

  # 报告内容配置
  content_modules:
    # 可配置的模块列表
    available_modules:
      - "pnl_analysis"           # 盈亏分析
      - "position_analysis"      # 持仓分析
      - "return_rate_analysis"   # 收益率分析
      - "performance_analysis"   # 性能分析
      - "risk_analysis"          # 风险分析
      - "contribution_analysis"  # 贡献分析
      - "drawdown_analysis"      # 回撤分析

    # 默认包含的模块
    default_modules:
      - "pnl_analysis"
      - "return_rate_analysis"
      - "performance_analysis"
      - "risk_analysis"
      - "contribution_analysis"

    # 模块依赖关系
    module_dependencies:
      risk_analysis: ["performance_analysis"]
      contribution_analysis: ["pnl_analysis"]
      drawdown_analysis: ["return_rate_analysis"]

  # Excel配置
  excel:
    # 工作表配置
    worksheets:
      - name: "每日表现摘要"
        content: "daily_performance_summary"
        enabled: true
      - name: "盈亏分析"
        content: "pnl_analysis"
        enabled: true
      - name: "持仓分析"
        content: "position_analysis"
        enabled: true
      - name: "收益率分析"
        content: "return_rate_analysis"
        enabled: true
      - name: "策略概览"
        content: "strategy_overview"
        enabled: true
      - name: "收益分析"
        content: "return_analysis"
        enabled: true
      - name: "风险指标"
        content: "risk_metrics"
        enabled: true
      - name: "贡献分析"
        content: "contribution_analysis"
        enabled: true
      - name: "行业分析"
        content: "industry_analysis"
        enabled: true
      - name: "信号频率分析"
        content: "signal_freq_analysis"
        enabled: true

    # 条件格式化
    conditional_formatting:
      profit_loss:
        positive_color: "#C6EFCE"  # 浅绿色
        negative_color: "#FFC7CE"  # 浅红色

      performance_ranking:
        top_color: "#00B050"       # 深绿色
        bottom_color: "#FF0000"    # 红色

  # Markdown配置
  markdown:
    include_charts: true
    chart_format: "png"

  # 策略建议配置
  recommendations:
    # 资金管理建议阈值
    allocation_thresholds:
      high_performance: 0.15    # 高表现策略阈值（夏普比率）
      low_performance: -0.1     # 低表现策略阈值

    # 风险管理建议阈值
    risk_thresholds:
      max_drawdown: 0.2         # 最大回撤阈值
      var_95: 0.05              # VaR 95%阈值

    # 相关性分析阈值
    correlation_thresholds:
      high_correlation: 0.7     # 高相关性阈值
      diversification: 0.3      # 分散化阈值

# 报告配置文件分离
report_config:
  # 是否启用独立报告配置文件
  enable_separate_config: true

  # 独立配置文件路径
  separate_config_path: "config/report_config.yaml"

  # 配置文件优先级：'main' 或 'separate'
  config_priority: "separate"

# 输出配置
output:
  base_directory: "output/cta_analysis"
  formats: ["excel", "png", "html", "markdown"]

  # 文件命名配置
  file_naming:
    timestamp_format: "%Y%m%d_%H%M%S"
    include_timestamp: true

# 存储配置
storage:
  # 输出路径（相对于项目根目录）
  base_path: "output/cta_analysis"

  # 文件命名
  use_timestamp: true
  timestamp_format: "%Y%m%d_%H%M%S"
  
  # 备份配置
  backup_enabled: true
  backup_retention_days: 30
  
  # 压缩配置
  compression: "gzip"
  
# 仪表板配置
dashboard:
  # 启用仪表板功能
  enabled: true

  # 默认时间段
  default_time_period: "daily"  # daily/weekly/monthly/yearly

  # 显示指标配置
  metrics:
    # 高优先级指标
    high_priority:
      - "today_pnl_summary"
      - "rolling_sharpe_ratio"
      - "return_distribution"

    # 中优先级指标
    medium_priority:
      - "strategy_performance"
      - "industry_performance"
      - "risk_metrics"

    # 低优先级指标
    low_priority:
      - "correlation_analysis"
      - "position_analysis"

  # 布局配置
  layout:
    # 网格布局 (rows, cols)
    grid_size: [3, 4]

    # 图表间距
    spacing: 0.3

    # 整体图表大小
    figure_size: [20, 15]

  # 输出配置
  output:
    # 静态图片格式
    static_format: "png"
    static_dpi: 300

    # 交互式格式
    interactive_format: "html"

    # 输出目录
    output_dir: "dashboard"

  # 样式配置
  style:
    # 主题
    theme: "professional"

    # 颜色主题
    color_theme: "high_contrast"

    # 字体配置
    font_family: "Arial"
    font_size: 10

# 持仓分析配置
position_analysis:
  # 启用持仓分析
  enabled: true

  # 集中度分析配置
  concentration_analysis:
    # 分析对象配置
    analysis_targets:
      - "all_positions"  # 所有持仓
      - "strategy_category"  # 按策略类别

    # 透视字段配置
    pivot_fields:
      - "industry"
      - "symbol_category"

    # 集中度计算配置
    calculation:
      # 计算指标
      metrics:
        - "position_amount"  # 持仓市值
        - "concentration_ratio"  # 集中度比例

      # 集中度比例计算方法
      concentration_method: "absolute_sum"  # 使用绝对值总和作为分母

    # 输出配置
    output:
      # 输出字段
      fields: ["symbol_category", "position_amount", "concentration_ratio"]
      # 排序方式
      sort_by: "concentration_ratio"
      sort_ascending: false

  # 持仓市值贡献比例分析配置
  contribution_analysis:
    # 分析对象配置
    analysis_targets:
      - "all_positions"  # 所有持仓
      - "strategy_category"  # 按策略类别

    # 透视字段
    pivot_field: "industry"

    # 贡献比例计算配置
    calculation:
      # 计算方法：'separate' 或 'combined'
      # separate: 正负持仓分别计算贡献比例
      # combined: 统一计算贡献比例
      method: "separate"

      # 正向贡献分母：所有正的industry持仓市值之和
      positive_denominator: "positive_sum"
      # 负向贡献分母：所有负的industry持仓市值之和
      negative_denominator: "negative_sum"

    # 输出配置
    output:
      # 输出字段
      fields: ["industry", "position_value", "contribution_ratio", "contribution_type"]
      # 排序方式
      sort_by: "contribution_ratio"
      sort_ascending: false

  # 轧差计算配置
  netting:
    # 是否启用多空轧差
    enable_long_short_netting: true

    # 轧差精度
    precision: 2

  # 持仓阈值配置
  thresholds:
    # 最小持仓金额（万元）
    min_position_amount: 1.0

    # 集中度阈值
    concentration_threshold: 0.3

  # 输出格式
  output_format:
    # 包含的字段
    fields: ["strategy_name", "industry", "net_position", "position_value", "position_direction"]

    # 排序方式
    sort_by: "position_value"
    sort_ascending: false

# 性能配置
performance:
  # 分析时间限制
  max_analysis_time: 60  # 秒

  # 图表质量
  chart_dpi: 300
  chart_success_rate_threshold: 0.95

  # 并行处理
  enable_parallel: true
  max_workers: 4

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "cta_analysis.log"
  max_file_size: "10MB"
  backup_count: 5
