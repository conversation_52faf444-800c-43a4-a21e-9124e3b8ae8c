# Return Rate Calculation Module Configuration
#
# This configuration file contains all settings specific to the return rate calculation module
# including strategy-position mapping, validation settings, and data storage options.

# ================================
# Return Rate Calculation Configuration
# ================================
return_rate_calculation:
  # Core calculation formula: 特定目标汇总的每日盈亏 / 特定目标的每日投资资金规模
  
  # Strategy-Position Mapping Configuration
  strategy_position_mapping:
    # Strategy level mapping (strategy_category -> position field)
    strategy_level_mapping:
      "other": "other"
      "option": "option" 
      "trend": "trend"
      "orderflow": "other"  # orderflow maps to other category
    
    # Stock index futures configuration
    stock_index_futures:
      symbols: ["IF", "IM", "IC", "IH"]
      position_field: "fin"
      description: "Stock index futures return calculation"
    
    # Treasury futures configuration  
    treasury_futures:
      symbols: ["T", "TS", "TF", "TL"]
      position_field: "fin"
      description: "Treasury futures return calculation"
    
    # Strategy signal mapping (strategy_signal -> position field)
    signal_mapping:
      "simple_boll": "boll"
      "simple_rsi": "rsi"
    
    # Custom mapping rules (can be extended)
    custom_mappings:
      # Example: specific symbol to position field mapping
      # "cu": "metal"
      # "al": "metal"
  
  # Data Validation Configuration
  validation:
    # Enable missing data warnings
    enable_missing_data_warnings: true
    
    # Missing data handling strategy
    missing_data_handling: "skip"  # "skip", "zero", "interpolate"
    
    # Minimum data points required for calculation
    min_data_points: 10
    
    # Maximum allowed missing data ratio
    max_missing_ratio: 0.2
    
    # Enable outlier detection
    enable_outlier_detection: true
    
    # Outlier detection method
    outlier_method: "iqr"  # "iqr", "zscore", "isolation_forest"
    
    # Outlier threshold
    outlier_threshold: 3.0
  
  # Data Storage Configuration
  output:
    # Storage path for return rate data
    storage_path: "output/cta_analysis/return_rates"
    
    # Use replacement mode (overwrite existing files)
    use_replacement_mode: true
    
    # File formats to generate
    file_formats:
      - "csv"
      - "excel"
      - "parquet"  # For large datasets
    
    # File naming convention
    file_naming:
      prefix: "return_rates"
      include_timestamp: true
      timestamp_format: "%Y%m%d_%H%M%S"
      include_strategy_suffix: true

# ================================
# Calculation Methods Configuration
# ================================
calculation_methods:
  # Return rate calculation method
  return_calculation_method: "daily_pnl_over_position"
  
  # Alternative methods (for future implementation)
  # - "cumulative_return"
  # - "log_return"
  # - "simple_return"
  
  # Annualization settings
  annualization:
    # Trading days per year
    trading_days_per_year: 252
    
    # Enable annualized return calculation
    calculate_annualized: true
    
    # Enable annualized volatility calculation
    calculate_annualized_volatility: true
  
  # Risk-free rate settings
  risk_free_rate:
    # Annual risk-free rate (for Sharpe ratio calculation)
    annual_rate: 0.03
    
    # Source of risk-free rate
    source: "manual"  # "manual", "file", "api"
    
    # Update frequency
    update_frequency: "monthly"

# ================================
# Data Quality Configuration
# ================================
data_quality:
  # Enable data quality checks
  enabled: true
  
  # Required fields for return calculation
  required_fields:
    cta_data:
      - "profit_loss_amount"
      - "trade_date"
      - "strategy_category"
    position_data:
      - "date"
      - "other"
      - "option"
      - "trend"
      - "fin"
  
  # Data consistency checks
  consistency_checks:
    # Check date alignment between CTA and position data
    check_date_alignment: true
    
    # Check for duplicate dates
    check_duplicate_dates: true
    
    # Check for data gaps
    check_data_gaps: true
    
    # Maximum allowed gap (days)
    max_gap_days: 5
  
  # Data cleaning settings
  cleaning:
    # Remove weekends and holidays
    remove_non_trading_days: true
    
    # Fill missing position data method
    fill_missing_positions: "forward_fill"  # "forward_fill", "zero", "interpolate"
    
    # Remove outliers
    remove_outliers: false

# ================================
# Performance Metrics Configuration
# ================================
performance_metrics:
  # Enable performance metrics calculation
  enabled: true
  
  # Metrics to calculate
  metrics:
    - "total_return"
    - "annualized_return"
    - "volatility"
    - "sharpe_ratio"
    - "max_drawdown"
    - "win_rate"
    - "profit_factor"
  
  # Rolling metrics settings
  rolling_metrics:
    # Enable rolling metrics calculation
    enabled: true
    
    # Rolling window sizes (days)
    window_sizes: [30, 60, 90, 180, 252]
    
    # Metrics to calculate on rolling basis
    rolling_metrics_list:
      - "return"
      - "volatility"
      - "sharpe_ratio"

# ================================
# Reporting Configuration
# ================================
reporting:
  # Enable automatic report generation
  enabled: true
  
  # Report formats
  formats:
    - "excel"
    - "csv"
    - "html"
  
  # Report content
  content:
    # Include summary statistics
    include_summary: true
    
    # Include detailed time series
    include_time_series: true
    
    # Include validation results
    include_validation: true
    
    # Include performance metrics
    include_performance: true
  
  # Chart generation
  charts:
    # Enable chart generation
    enabled: true
    
    # Chart types to generate
    chart_types:
      - "return_series"
      - "cumulative_return"
      - "rolling_sharpe"
      - "drawdown"
    
    # Chart format
    format: "png"
    
    # Chart resolution (DPI)
    dpi: 300

# ================================
# Error Handling Configuration
# ================================
error_handling:
  # Continue processing on errors
  continue_on_error: true
  
  # Log all errors
  log_errors: true
  
  # Error notification settings
  notifications:
    # Enable error notifications
    enabled: false
    
    # Notification methods
    methods: ["log", "email"]
    
    # Email settings (if enabled)
    email:
      smtp_server: "localhost"
      smtp_port: 587
      sender: "<EMAIL>"
      recipients: ["<EMAIL>"]

# ================================
# Advanced Configuration
# ================================
advanced:
  # Enable experimental features
  experimental_features: false
  
  # Custom calculation functions
  custom_functions:
    # Enable custom return calculation functions
    enabled: false
    
    # Custom function module path
    module_path: "custom.return_calculations"
  
  # Parallel processing
  parallel_processing:
    # Enable parallel processing
    enabled: true
    
    # Number of worker processes
    num_workers: 4
    
    # Minimum data size for parallel processing
    min_data_size: 1000
  
  # Memory management
  memory_management:
    # Enable memory optimization
    enabled: true
    
    # Chunk size for large datasets
    chunk_size: 10000
    
    # Enable garbage collection
    enable_gc: true

# ================================
# Logging Configuration
# ================================
logging:
  # Log level
  level: "INFO"
  
  # Enable detailed logging
  detailed_logging: true
  
  # Log calculation steps
  log_calculation_steps: true
  
  # Log validation results
  log_validation_results: true
  
  # Log performance metrics
  log_performance_metrics: true
