# CTA Analysis System - Master Configuration File
#
# This is the master configuration file that references all module-specific configurations.
# It provides a centralized way to manage the entire system configuration while maintaining
# modular configuration architecture.

# ================================
# System Information
# ================================
system:
  name: "CTA Strategy Analysis System"
  version: "2.0.0"
  description: "Comprehensive CTA strategy analysis with modular configuration architecture"
  last_updated: "2025-06-17"

# ================================
# Configuration Architecture
# ================================
configuration:
  # Configuration file references
  module_configs:
    pnl_analysis: "pnl_analysis_config.yaml"
    position_analysis: "position_analysis_config.yaml"
    return_rate_calculation: "return_rate_config.yaml"
    risk_analysis: "risk_analysis_config.yaml"
    reporting: "reporting_config.yaml"
  
  # Configuration validation
  validation:
    enabled: true
    strict_mode: false
    validate_on_startup: true
  
  # Configuration reloading
  hot_reload:
    enabled: false
    watch_files: true
    reload_interval: 60  # seconds

# ================================
# Data Configuration
# ================================
data:
  # File paths
  file_paths:
    # CTA策略数据文件路径
    processed_cta_data: "input/processed_cta_data.csv"
    
    # 策略持仓数据文件路径
    strategy_position: "input/strategy_position.csv"
  
  # Data loading settings
  loading:
    # Encoding settings
    encoding: "utf-8-sig"
    fallback_encodings: ["utf-8", "gbk", "gb2312"]
    
    # Data validation
    validate_on_load: true
    skip_bad_lines: true
    
    # Memory optimization
    use_chunks: false
    chunk_size: 10000
  
  # Column mappings
  column_mappings:
    # CTA data columns
    cta_data:
      date_column: "trade_date"
      pnl_column: "profit_loss_amount"
      strategy_category_column: "strategy_category"
      strategy_signal_column: "strategy_signal"
      signal_freq_column: "signal_freq"
      symbol_category_column: "symbol_category"
      industry_column: "industry"
      position_direction_column: "position_direction"
      position_amount_column: "position_amount"
    
    # Position data columns
    position_data:
      date_column: "date"
      strategy_columns:
        other: "other"
        option: "option"
        trend: "trend"
        fin: "fin"
        boll: "boll"
        rsi: "rsi"

# ================================
# Analysis Configuration
# ================================
analysis:
  # Global analysis settings
  global_settings:
    # Time zone
    timezone: "Asia/Shanghai"
    
    # Trading calendar
    trading_calendar: "china"
    
    # Risk-free rate
    risk_free_rate: 0.03
    
    # Currency
    base_currency: "CNY"
  
  # Module enablement
  enabled_modules:
    pnl_analysis: true
    position_analysis: true
    return_rate_calculation: true
    performance_analysis: true
    risk_analysis: true
    contribution_analysis: true
    drawdown_analysis: true
    reporting: true
  
  # Time windows configuration
  time_windows:
    # Calculation method for time windows
    calculation_method: "calendar_based"  # "calendar_based" or "rolling"
    
    # Supported time windows
    supported_windows:
      - "daily"
      - "weekly"
      - "monthly"
      - "quarterly"
      - "yearly"
      - "custom"
    
    # Default time windows for analysis
    default_windows:
      - "daily"
      - "weekly"
      - "monthly"
      - "yearly"

# ================================
# Output Configuration
# ================================
output:
  # Base output directory
  base_directory: "output"

  # Standardized directory structure
  directory_structure:
    charts:
      performance: "charts/performance"
      risk: "charts/risk"
      contribution: "charts/contribution"
      quantstats: "charts/quantstats"
      position: "charts/position"
      pnl: "charts/pnl"
      drawdown: "charts/drawdown"
    reports:
      excel: "reports/excel"
      html: "reports/html"
      markdown: "reports/markdown"
      timeframe_reports: "reports/timeframe_reports"
      comprehensive: "reports/comprehensive"
    data_exports:
      return_rates: "data_exports/return_rates"
      analysis_results: "data_exports/analysis_results"
      raw_data: "data_exports/raw_data"
      processed_data: "data_exports/processed_data"
    logs: "logs"
    cache: "cache"
    temp: "temp"
  
  # File management
  file_management:
    # Create directories automatically
    auto_create_directories: true

    # File naming convention
    naming_convention:
      include_timestamp: true
      timestamp_format: "%Y%m%d_%H%M%S"
      include_module_name: true
      use_underscores: true
      separators:
        module: "_"
        type: "_"
        timestamp: "_"

    # File retention and cleanup policies
    cleanup_policies:
      enabled: true
      max_files_per_directory: 50
      max_age_days: 30
      cleanup_on_startup: true
      preserve_patterns:
        - "*_final*"
        - "*_important*"
        - "*_archive*"

    # Output organization
    organization:
      use_standardized_structure: true
      create_subdirectories: true
      enforce_naming_conventions: true

# ================================
# Performance Configuration
# ================================
performance:
  # Global performance settings
  global:
    # Enable performance monitoring
    monitoring_enabled: true
    
    # Performance logging
    log_performance: true
    
    # Memory management
    memory_limit: "4GB"
    enable_garbage_collection: true
  
  # Parallel processing
  parallel_processing:
    # Enable parallel processing
    enabled: true
    
    # Number of worker processes (0 = auto-detect)
    num_workers: 0
    
    # Minimum data size for parallel processing
    min_data_size: 1000
  
  # Caching
  caching:
    # Global caching settings
    enabled: true
    
    # Cache directory
    cache_directory: "cache"
    
    # Default cache expiry (seconds)
    default_expiry: 3600
    
    # Maximum cache size (MB)
    max_cache_size: 500

# ================================
# Logging Configuration
# ================================
logging:
  # Global logging settings
  global:
    # Log level
    level: "INFO"
    
    # Log format
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Date format
    date_format: "%Y-%m-%d %H:%M:%S"
  
  # Log files
  files:
    # Main log file
    main_log: "logs/cta_analysis.log"
    
    # Error log file
    error_log: "logs/cta_analysis_error.log"
    
    # Performance log file
    performance_log: "logs/cta_analysis_performance.log"
  
  # Log rotation
  rotation:
    enabled: true
    max_file_size: "10MB"
    backup_count: 5
    rotation_frequency: "daily"
  
  # Module-specific logging
  modules:
    pnl_analysis: "INFO"
    position_analysis: "INFO"
    return_rate_calculation: "INFO"
    risk_analysis: "INFO"
    reporting: "INFO"

# ================================
# Security Configuration
# ================================
security:
  # Data security
  data_security:
    # Enable data encryption
    encryption_enabled: false
    
    # Encryption algorithm
    encryption_algorithm: "AES-256"
    
    # Key management
    key_management: "file"  # "file", "environment", "vault"
  
  # Access control
  access_control:
    # Enable access control
    enabled: false
    
    # Authentication method
    authentication: "none"  # "none", "basic", "token", "oauth"
    
    # Authorization
    authorization: "none"   # "none", "role_based", "permission_based"

# ================================
# Integration Configuration
# ================================
integration:
  # External systems
  external_systems:
    # Database integration
    database:
      enabled: false
      type: "postgresql"  # "postgresql", "mysql", "sqlite", "mongodb"
      connection_string: ""
    
    # API integration
    api:
      enabled: false
      base_url: ""
      authentication: "none"
    
    # Message queue integration
    message_queue:
      enabled: false
      type: "rabbitmq"  # "rabbitmq", "kafka", "redis"
      connection_string: ""
  
  # Third-party libraries
  third_party:
    # Quantstats integration
    quantstats:
      enabled: true
      version: "latest"
      features:
        - "performance_metrics"
        - "risk_metrics"
        - "visualizations"
        - "html_reports"
    
    # Other integrations
    plotly:
      enabled: true
      version: "latest"
    
    matplotlib:
      enabled: true
      backend: "Agg"

# ================================
# Development Configuration
# ================================
development:
  # Debug settings
  debug:
    enabled: false
    verbose_logging: false
    save_intermediate_results: false
  
  # Testing
  testing:
    # Test data directory
    test_data_directory: "tests/data"
    
    # Enable test mode
    test_mode: false
    
    # Mock external dependencies
    mock_external_deps: false
  
  # Profiling
  profiling:
    enabled: false
    profiler: "cProfile"  # "cProfile", "line_profiler", "memory_profiler"
    output_directory: "profiling"

# ================================
# Deployment Configuration
# ================================
deployment:
  # Environment
  environment: "development"  # "development", "staging", "production"
  
  # Deployment settings
  settings:
    # Enable production optimizations
    production_optimizations: false
    
    # Error reporting
    error_reporting: "log"  # "log", "email", "webhook", "none"
    
    # Health checks
    health_checks: false
  
  # Monitoring
  monitoring:
    # Enable monitoring
    enabled: false
    
    # Monitoring system
    system: "none"  # "none", "prometheus", "datadog", "newrelic"
    
    # Metrics collection
    collect_metrics: false
