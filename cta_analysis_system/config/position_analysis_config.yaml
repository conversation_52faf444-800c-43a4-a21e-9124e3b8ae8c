# Position Analysis Module Configuration
#
# This configuration file contains all settings specific to the position analysis module
# including concentration analysis, contribution analysis, and netting calculations.

# ================================
# Position Analysis Configuration
# ================================
position_analysis:
  # Enable/disable position analysis
  enabled: true
  
  # Analysis scope
  scope:
    # Include current positions
    include_current: true
    
    # Include historical positions
    include_historical: true
    
    # Historical analysis period (days)
    historical_period: 30

# ================================
# Concentration Analysis Configuration
# ================================
concentration_analysis:
  # Analysis targets
  analysis_targets:
    - "all_positions"      # Analyze all positions together
    - "strategy_category"  # Analyze by strategy category
  
  # Pivot fields for concentration analysis
  pivot_fields:
    - "industry"
    - "symbol_category"
  
  # Calculation settings
  calculation:
    # Metrics to calculate
    metrics:
      - "position_amount"      # Absolute position amount
      - "concentration_ratio"  # Concentration percentage
    
    # Concentration calculation method
    concentration_method: "absolute_sum"  # Use absolute value sum as denominator
    
    # Alternative methods: "net_sum", "gross_sum"
  
  # Output configuration
  output:
    # Output fields
    fields:
      - "symbol_category"      # or "industry" depending on pivot field
      - "position_amount"
      - "concentration_ratio"
    
    # Sorting configuration
    sort_by: "concentration_ratio"
    sort_ascending: false
    
    # Top N results to return (0 = all)
    top_n: 0
  
  # Thresholds and alerts
  thresholds:
    # High concentration threshold (%)
    high_concentration: 0.30
    
    # Medium concentration threshold (%)
    medium_concentration: 0.15
    
    # Alert when concentration exceeds threshold
    enable_alerts: true

# ================================
# Contribution Analysis Configuration
# ================================
contribution_analysis:
  # Analysis targets
  analysis_targets:
    - "all_positions"      # Analyze all positions together
    - "strategy_category"  # Analyze by strategy category
  
  # Pivot field for contribution analysis
  pivot_field: "industry"
  
  # Calculation settings
  calculation:
    # Calculation method
    method: "separate"  # "separate" or "combined"
    
    # For separate method:
    # - Positive contributions: denominator = sum of all positive values
    # - Negative contributions: denominator = sum of all negative values
    
    # Denominator settings
    positive_denominator: "positive_sum"
    negative_denominator: "negative_sum"
  
  # Output configuration
  output:
    # Output fields
    fields:
      - "industry"
      - "position_value"
      - "contribution_ratio"
      - "contribution_type"  # "positive" or "negative"
    
    # Sorting configuration
    sort_by: "contribution_ratio"
    sort_ascending: false
    
    # Include contribution type in output
    include_contribution_type: true

# ================================
# Netting Configuration
# ================================
netting:
  # Enable/disable long-short netting
  enable_long_short_netting: true
  
  # Netting precision (decimal places)
  precision: 2
  
  # Netting method
  method: "by_symbol"  # "by_symbol", "by_industry", "by_strategy"
  
  # Minimum position threshold for netting
  min_position_threshold: 0.01

# ================================
# Position Thresholds Configuration
# ================================
thresholds:
  # Minimum position amount (万元)
  min_position_amount: 1.0
  
  # Maximum position amount (万元) - for validation
  max_position_amount: 100000.0
  
  # Concentration threshold for alerts
  concentration_threshold: 0.3
  
  # Position change threshold for alerts (%)
  position_change_threshold: 0.1

# ================================
# Output Format Configuration
# ================================
output_format:
  # Fields to include in standard output
  fields:
    - "strategy_name"
    - "industry"
    - "net_position"
    - "position_value"
    - "position_direction"
  
  # Sorting configuration
  sort_by: "position_value"
  sort_ascending: false
  
  # Number formatting
  number_format:
    # Decimal places for position amounts
    position_decimals: 2
    
    # Decimal places for percentages
    percentage_decimals: 4
    
    # Use thousands separator
    thousands_separator: true

# ================================
# Data Validation Configuration
# ================================
validation:
  # Enable data validation
  enabled: true
  
  # Required fields
  required_fields:
    - "position_amount"
    - "strategy_category"
    - "industry"
  
  # Data quality checks
  quality_checks:
    # Check for zero positions
    check_zero_positions: true
    
    # Check for negative positions in long-only strategies
    check_negative_positions: true
    
    # Check for position limits
    check_position_limits: true
  
  # Position validation rules
  position_rules:
    # Maximum leverage allowed
    max_leverage: 3.0
    
    # Maximum single position size (% of total)
    max_single_position: 0.5
    
    # Maximum industry concentration (% of total)
    max_industry_concentration: 0.6

# ================================
# Risk Management Configuration
# ================================
risk_management:
  # Enable risk management checks
  enabled: true
  
  # Position size limits
  position_limits:
    # Maximum position size per symbol (万元)
    max_position_per_symbol: 10000
    
    # Maximum position size per industry (万元)
    max_position_per_industry: 50000
    
    # Maximum total position size (万元)
    max_total_position: 200000
  
  # Concentration limits
  concentration_limits:
    # Maximum concentration per symbol (%)
    max_symbol_concentration: 0.2
    
    # Maximum concentration per industry (%)
    max_industry_concentration: 0.4
    
    # Maximum concentration per strategy (%)
    max_strategy_concentration: 0.6
  
  # Alert settings
  alerts:
    # Enable position limit alerts
    position_limit_alerts: true
    
    # Enable concentration alerts
    concentration_alerts: true
    
    # Alert threshold (% of limit)
    alert_threshold: 0.8

# ================================
# Performance Configuration
# ================================
performance:
  # Enable performance optimizations
  optimizations_enabled: true
  
  # Caching settings
  caching:
    enabled: true
    expiry_time: 1800  # 30 minutes
    max_size: 50       # MB
  
  # Batch processing settings
  batch_processing:
    # Enable batch processing for large datasets
    enabled: true
    
    # Batch size
    batch_size: 1000
    
    # Minimum data size for batch processing
    min_data_size: 5000

# ================================
# Logging Configuration
# ================================
logging:
  # Log level
  level: "INFO"
  
  # Enable detailed logging
  detailed_logging: true
  
  # Log position changes
  log_position_changes: true
  
  # Log risk alerts
  log_risk_alerts: true
