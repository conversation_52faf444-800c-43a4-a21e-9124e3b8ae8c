#!/usr/bin/env python3
"""
Modular Configuration Manager

This module provides a new configuration management system that supports
modular configuration architecture with separate config files for each module.
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
import os

logger = logging.getLogger(__name__)


@dataclass
class ModuleConfig:
    """Base class for module-specific configurations"""
    module_name: str
    config_file: str
    config_data: Dict[str, Any] = field(default_factory=dict)
    is_loaded: bool = False
    last_modified: Optional[float] = None


class ModularConfigManager:
    """
    Modular Configuration Manager
    
    Manages configuration files for different modules while maintaining
    backward compatibility with the existing system.
    """
    
    def __init__(self, master_config_path: str = "config/master_config.yaml"):
        """
        Initialize the modular configuration manager
        
        Args:
            master_config_path: Path to the master configuration file
        """
        self.master_config_path = Path(master_config_path)
        self.config_dir = self.master_config_path.parent
        self.master_config = {}
        self.module_configs = {}
        self.is_initialized = False
        
    def initialize(self) -> bool:
        """
        Initialize the configuration manager
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            # Load master configuration
            if not self._load_master_config():
                logger.error("Failed to load master configuration")
                return False
            
            # Load module configurations
            if not self._load_module_configs():
                logger.error("Failed to load module configurations")
                return False
            
            self.is_initialized = True
            logger.info("Modular configuration manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize configuration manager: {e}")
            return False
    
    def _load_master_config(self) -> bool:
        """Load the master configuration file"""
        try:
            if not self.master_config_path.exists():
                logger.error(f"Master config file not found: {self.master_config_path}")
                return False
            
            with open(self.master_config_path, 'r', encoding='utf-8') as f:
                self.master_config = yaml.safe_load(f)
            
            logger.info(f"Loaded master configuration from {self.master_config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error loading master config: {e}")
            return False
    
    def _load_module_configs(self) -> bool:
        """Load all module configuration files"""
        try:
            module_config_refs = self.master_config.get('configuration', {}).get('module_configs', {})
            
            for module_name, config_file in module_config_refs.items():
                config_path = self.config_dir / config_file
                
                if not config_path.exists():
                    logger.warning(f"Module config file not found: {config_path}")
                    continue
                
                try:
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config_data = yaml.safe_load(f)
                    
                    module_config = ModuleConfig(
                        module_name=module_name,
                        config_file=str(config_path),
                        config_data=config_data,
                        is_loaded=True,
                        last_modified=config_path.stat().st_mtime
                    )
                    
                    self.module_configs[module_name] = module_config
                    logger.info(f"Loaded module config: {module_name}")
                    
                except Exception as e:
                    logger.error(f"Error loading module config {module_name}: {e}")
                    continue
            
            logger.info(f"Loaded {len(self.module_configs)} module configurations")
            return True
            
        except Exception as e:
            logger.error(f"Error loading module configs: {e}")
            return False
    
    def get_module_config(self, module_name: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific module
        
        Args:
            module_name: Name of the module
            
        Returns:
            Module configuration dictionary or None if not found
        """
        if not self.is_initialized:
            logger.warning("Configuration manager not initialized")
            return None
        
        if module_name not in self.module_configs:
            logger.warning(f"Module config not found: {module_name}")
            return None
        
        return self.module_configs[module_name].config_data
    
    def get_master_config(self) -> Dict[str, Any]:
        """
        Get the master configuration
        
        Returns:
            Master configuration dictionary
        """
        return self.master_config.copy()
    
    def get_data_config(self) -> Dict[str, Any]:
        """
        Get data configuration from master config
        
        Returns:
            Data configuration dictionary
        """
        return self.master_config.get('data', {})
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """
        Get analysis configuration from master config
        
        Returns:
            Analysis configuration dictionary
        """
        return self.master_config.get('analysis', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        Get output configuration from master config
        
        Returns:
            Output configuration dictionary
        """
        return self.master_config.get('output', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """
        Get logging configuration from master config
        
        Returns:
            Logging configuration dictionary
        """
        return self.master_config.get('logging', {})
    
    def get_performance_config(self) -> Dict[str, Any]:
        """
        Get performance configuration from master config
        
        Returns:
            Performance configuration dictionary
        """
        return self.master_config.get('performance', {})
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate all configurations
        
        Returns:
            Validation results dictionary
        """
        validation_results = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'module_status': {}
        }
        
        try:
            # Validate master config
            if not self.master_config:
                validation_results['errors'].append("Master configuration is empty")
                validation_results['is_valid'] = False
            
            # Validate module configs
            for module_name, module_config in self.module_configs.items():
                module_validation = self._validate_module_config(module_name, module_config)
                validation_results['module_status'][module_name] = module_validation
                
                if not module_validation['is_valid']:
                    validation_results['is_valid'] = False
                    validation_results['errors'].extend(module_validation['errors'])
                
                validation_results['warnings'].extend(module_validation['warnings'])
            
            # Check for required modules
            required_modules = ['pnl_analysis', 'position_analysis', 'return_rate_calculation', 'risk_analysis', 'reporting']
            for module in required_modules:
                if module not in self.module_configs:
                    validation_results['warnings'].append(f"Required module config missing: {module}")
            
            logger.info(f"Configuration validation completed. Valid: {validation_results['is_valid']}")
            return validation_results
            
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            validation_results['is_valid'] = False
            validation_results['errors'].append(f"Validation error: {e}")
            return validation_results
    
    def _validate_module_config(self, module_name: str, module_config: ModuleConfig) -> Dict[str, Any]:
        """Validate a specific module configuration"""
        validation = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            if not module_config.is_loaded:
                validation['errors'].append(f"Module {module_name} not loaded")
                validation['is_valid'] = False
                return validation
            
            if not module_config.config_data:
                validation['errors'].append(f"Module {module_name} has empty configuration")
                validation['is_valid'] = False
                return validation
            
            # Module-specific validation
            if module_name == 'pnl_analysis':
                self._validate_pnl_config(module_config.config_data, validation)
            elif module_name == 'position_analysis':
                self._validate_position_config(module_config.config_data, validation)
            elif module_name == 'return_rate_calculation':
                self._validate_return_rate_config(module_config.config_data, validation)
            elif module_name == 'risk_analysis':
                self._validate_risk_config(module_config.config_data, validation)
            elif module_name == 'reporting':
                self._validate_reporting_config(module_config.config_data, validation)
            
        except Exception as e:
            validation['errors'].append(f"Validation error for {module_name}: {e}")
            validation['is_valid'] = False
        
        return validation
    
    def _validate_pnl_config(self, config: Dict[str, Any], validation: Dict[str, Any]):
        """Validate PnL analysis configuration"""
        if 'pnl_analysis' not in config:
            validation['errors'].append("Missing pnl_analysis section")
            validation['is_valid'] = False
    
    def _validate_position_config(self, config: Dict[str, Any], validation: Dict[str, Any]):
        """Validate position analysis configuration"""
        if 'position_analysis' not in config:
            validation['errors'].append("Missing position_analysis section")
            validation['is_valid'] = False
    
    def _validate_return_rate_config(self, config: Dict[str, Any], validation: Dict[str, Any]):
        """Validate return rate calculation configuration"""
        if 'return_rate_calculation' not in config:
            validation['errors'].append("Missing return_rate_calculation section")
            validation['is_valid'] = False
    
    def _validate_risk_config(self, config: Dict[str, Any], validation: Dict[str, Any]):
        """Validate risk analysis configuration"""
        if 'risk_analysis' not in config:
            validation['errors'].append("Missing risk_analysis section")
            validation['is_valid'] = False
    
    def _validate_reporting_config(self, config: Dict[str, Any], validation: Dict[str, Any]):
        """Validate reporting configuration"""
        if 'timeframe_reporting' not in config:
            validation['errors'].append("Missing timeframe_reporting section")
            validation['is_valid'] = False
    
    def reload_configuration(self) -> bool:
        """
        Reload all configuration files
        
        Returns:
            True if reload successful, False otherwise
        """
        try:
            logger.info("Reloading configuration...")
            
            # Clear existing configs
            self.master_config = {}
            self.module_configs = {}
            self.is_initialized = False
            
            # Reinitialize
            return self.initialize()
            
        except Exception as e:
            logger.error(f"Failed to reload configuration: {e}")
            return False
    
    def get_legacy_config(self) -> Dict[str, Any]:
        """
        Get configuration in legacy format for backward compatibility
        
        Returns:
            Configuration in legacy format
        """
        try:
            # Combine all configurations into legacy format
            legacy_config = {
                'data': self.get_data_config(),
                'analysis': self.get_analysis_config(),
                'output': self.get_output_config(),
                'logging': self.get_logging_config(),
                'performance': self.get_performance_config()
            }
            
            # Add module-specific configs to analysis section
            for module_name, module_config in self.module_configs.items():
                if module_config.is_loaded and module_config.config_data:
                    # Extract the main config section from each module
                    main_section = module_config.config_data.get(module_name, {})
                    if main_section:
                        legacy_config['analysis'][module_name] = main_section
            
            return legacy_config
            
        except Exception as e:
            logger.error(f"Failed to generate legacy config: {e}")
            return {}


# Global instance for backward compatibility
modular_config_manager = ModularConfigManager()


def get_modular_config_manager() -> ModularConfigManager:
    """Get the global modular configuration manager instance"""
    return modular_config_manager
