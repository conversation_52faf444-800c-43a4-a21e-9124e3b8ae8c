#!/usr/bin/env python3
"""
收益率计算模块 (Return Rate Calculator)

实现CTA策略的收益率计算功能，包括：
1. 策略-持仓映射逻辑
2. 多维度收益率计算
3. 数据验证和缺失提示
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os

logger = logging.getLogger(__name__)


class ReturnRateCalculator:
    """收益率计算器"""
    
    def __init__(self, config):
        """
        初始化收益率计算器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.return_config = config.analysis.return_rate_calculation
        self.validation_config = self.return_config.validation
        
    def calculate_strategy_category_returns(self, cta_data: pd.DataFrame, 
                                          position_data: pd.DataFrame) -> Dict[str, Any]:
        """
        按strategy_category计算收益率
        
        Args:
            cta_data: CTA数据
            position_data: 持仓数据
            
        Returns:
            策略类别收益率结果
        """
        logger.info("Calculating strategy category returns")
        
        try:
            results = {}
            mapping = self.return_config.strategy_position_mapping.get('strategy_level_mapping', {})
            
            for strategy_category, position_field in mapping.items():
                logger.info(f"Processing strategy: {strategy_category} -> position field: {position_field}")
                
                # 筛选策略数据
                strategy_data = cta_data[cta_data['strategy_category'] == strategy_category].copy()
                
                if strategy_data.empty:
                    logger.warning(f"No data found for strategy: {strategy_category}")
                    continue
                
                # 计算每日盈亏
                daily_pnl = strategy_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
                daily_pnl.columns = ['date', 'daily_pnl']
                
                # 获取对应的持仓数据
                if position_field in position_data.columns:
                    position_subset = position_data[['date', position_field]].copy()
                    position_subset.columns = ['date', 'position_amount']
                    
                    # 合并数据
                    merged_data = pd.merge(daily_pnl, position_subset, on='date', how='inner')
                    
                    # 验证数据
                    validation_result = self._validate_return_calculation_data(
                        merged_data, strategy_category, position_field
                    )
                    
                    if validation_result['valid']:
                        # 计算收益率
                        merged_data['return_rate'] = merged_data['daily_pnl'] / merged_data['position_amount']
                        merged_data['return_rate'] = merged_data['return_rate'].fillna(0)
                        
                        results[strategy_category] = {
                            'return_series': merged_data[['date', 'return_rate']].to_dict('records'),
                            'daily_pnl_series': merged_data[['date', 'daily_pnl']].to_dict('records'),
                            'position_series': merged_data[['date', 'position_amount']].to_dict('records'),
                            'statistics': self._calculate_return_statistics(merged_data['return_rate']),
                            'validation': validation_result
                        }
                    else:
                        results[strategy_category] = {
                            'error': 'Data validation failed',
                            'validation': validation_result
                        }
                else:
                    logger.warning(f"Position field '{position_field}' not found for strategy '{strategy_category}'")
                    results[strategy_category] = {
                        'error': f"Position field '{position_field}' not found",
                        'validation': {'valid': False, 'warnings': [f"Missing position field: {position_field}"]}
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"Strategy category returns calculation failed: {e}")
            raise
    
    def calculate_specific_futures_returns(self, cta_data: pd.DataFrame, 
                                         position_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算特定期货品种收益率（股指期货、国债期货）
        
        Args:
            cta_data: CTA数据
            position_data: 持仓数据
            
        Returns:
            特定期货收益率结果
        """
        logger.info("Calculating specific futures returns")
        
        try:
            results = {}
            
            # 股指期货收益率
            stock_index_config = self.return_config.strategy_position_mapping.get('stock_index_futures', {})
            if stock_index_config:
                stock_index_returns = self._calculate_symbol_group_returns(
                    cta_data, position_data,
                    stock_index_config.get('symbols', []),
                    stock_index_config.get('position_field', 'fin'),
                    'stock_index_futures'
                )
                results['stock_index_futures'] = stock_index_returns

            # 国债期货收益率
            treasury_config = self.return_config.strategy_position_mapping.get('treasury_futures', {})
            if treasury_config:
                treasury_returns = self._calculate_symbol_group_returns(
                    cta_data, position_data,
                    treasury_config.get('symbols', []),
                    treasury_config.get('position_field', 'fin'),
                    'treasury_futures'
                )
                results['treasury_futures'] = treasury_returns
            
            return results
            
        except Exception as e:
            logger.error(f"Specific futures returns calculation failed: {e}")
            raise
    
    def calculate_signal_returns(self, cta_data: pd.DataFrame, 
                               position_data: pd.DataFrame) -> Dict[str, Any]:
        """
        计算策略信号收益率
        
        Args:
            cta_data: CTA数据
            position_data: 持仓数据
            
        Returns:
            策略信号收益率结果
        """
        logger.info("Calculating signal returns")
        
        try:
            results = {}
            signal_mapping = self.return_config.strategy_position_mapping.get('signal_mapping', {})
            
            for signal, position_field in signal_mapping.items():
                logger.info(f"Processing signal: {signal} -> position field: {position_field}")
                
                # 筛选信号数据
                signal_data = cta_data[cta_data['strategy_signal'] == signal].copy()
                
                if signal_data.empty:
                    logger.warning(f"No data found for signal: {signal}")
                    continue
                
                # 计算每日盈亏
                daily_pnl = signal_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
                daily_pnl.columns = ['date', 'daily_pnl']
                
                # 获取对应的持仓数据
                if position_field in position_data.columns:
                    position_subset = position_data[['date', position_field]].copy()
                    position_subset.columns = ['date', 'position_amount']
                    
                    # 合并数据
                    merged_data = pd.merge(daily_pnl, position_subset, on='date', how='inner')
                    
                    # 验证数据
                    validation_result = self._validate_return_calculation_data(
                        merged_data, signal, position_field
                    )
                    
                    if validation_result['valid']:
                        # 计算收益率
                        merged_data['return_rate'] = merged_data['daily_pnl'] / merged_data['position_amount']
                        merged_data['return_rate'] = merged_data['return_rate'].fillna(0)
                        
                        results[signal] = {
                            'return_series': merged_data[['date', 'return_rate']].to_dict('records'),
                            'daily_pnl_series': merged_data[['date', 'daily_pnl']].to_dict('records'),
                            'position_series': merged_data[['date', 'position_amount']].to_dict('records'),
                            'statistics': self._calculate_return_statistics(merged_data['return_rate']),
                            'validation': validation_result
                        }
                    else:
                        results[signal] = {
                            'error': 'Data validation failed',
                            'validation': validation_result
                        }
                else:
                    logger.warning(f"Position field '{position_field}' not found for signal '{signal}'")
                    results[signal] = {
                        'error': f"Position field '{position_field}' not found",
                        'validation': {'valid': False, 'warnings': [f"Missing position field: {position_field}"]}
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"Signal returns calculation failed: {e}")
            raise
    
    def _calculate_symbol_group_returns(self, cta_data: pd.DataFrame, 
                                      position_data: pd.DataFrame,
                                      symbols: List[str], position_field: str,
                                      group_name: str) -> Dict[str, Any]:
        """计算特定品种组合收益率"""
        try:
            # 筛选品种数据
            symbol_data = cta_data[cta_data['symbol_category'].isin(symbols)].copy()
            
            if symbol_data.empty:
                logger.warning(f"No data found for symbol group: {group_name}")
                return {'error': f"No data found for symbols: {symbols}"}
            
            # 计算每日盈亏
            daily_pnl = symbol_data.groupby('trade_date')['profit_loss_amount'].sum().reset_index()
            daily_pnl.columns = ['date', 'daily_pnl']
            
            # 获取对应的持仓数据
            if position_field in position_data.columns:
                position_subset = position_data[['date', position_field]].copy()
                position_subset.columns = ['date', 'position_amount']
                
                # 合并数据
                merged_data = pd.merge(daily_pnl, position_subset, on='date', how='inner')
                
                # 验证数据
                validation_result = self._validate_return_calculation_data(
                    merged_data, group_name, position_field
                )
                
                if validation_result['valid']:
                    # 计算收益率
                    merged_data['return_rate'] = merged_data['daily_pnl'] / merged_data['position_amount']
                    merged_data['return_rate'] = merged_data['return_rate'].fillna(0)
                    
                    return {
                        'symbols': symbols,
                        'return_series': merged_data[['date', 'return_rate']].to_dict('records'),
                        'daily_pnl_series': merged_data[['date', 'daily_pnl']].to_dict('records'),
                        'position_series': merged_data[['date', 'position_amount']].to_dict('records'),
                        'statistics': self._calculate_return_statistics(merged_data['return_rate']),
                        'validation': validation_result
                    }
                else:
                    return {
                        'symbols': symbols,
                        'error': 'Data validation failed',
                        'validation': validation_result
                    }
            else:
                logger.warning(f"Position field '{position_field}' not found for group '{group_name}'")
                return {
                    'symbols': symbols,
                    'error': f"Position field '{position_field}' not found",
                    'validation': {'valid': False, 'warnings': [f"Missing position field: {position_field}"]}
                }
                
        except Exception as e:
            logger.error(f"Symbol group returns calculation failed for {group_name}: {e}")
            return {'symbols': symbols, 'error': str(e)}
    
    def _validate_return_calculation_data(self, merged_data: pd.DataFrame, 
                                        target_name: str, position_field: str) -> Dict[str, Any]:
        """验证收益率计算数据"""
        validation_result = {
            'valid': True,
            'warnings': [],
            'errors': []
        }
        
        try:
            # 检查数据点数量
            if len(merged_data) < self.validation_config.min_data_points:
                validation_result['warnings'].append(
                    f"Insufficient data points for {target_name}: {len(merged_data)} < {self.validation_config.min_data_points}"
                )
            
            # 检查缺失的持仓数据
            zero_position_count = (merged_data['position_amount'] == 0).sum()
            if zero_position_count > 0:
                if self.validation_config.enable_missing_data_warnings:
                    validation_result['warnings'].append(
                        f"Warning: {target_name} strategy has {zero_position_count} days with zero position data. "
                        f"Return rates for these days will be set to zero."
                    )
            
            # 检查异常值
            if 'daily_pnl' in merged_data.columns:
                pnl_std = merged_data['daily_pnl'].std()
                pnl_mean = merged_data['daily_pnl'].mean()
                outliers = merged_data[abs(merged_data['daily_pnl'] - pnl_mean) > 3 * pnl_std]
                
                if len(outliers) > 0:
                    validation_result['warnings'].append(
                        f"Found {len(outliers)} potential outliers in PnL data for {target_name}"
                    )
            
            # 如果有错误，标记为无效
            if validation_result['errors']:
                validation_result['valid'] = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Data validation failed for {target_name}: {e}")
            return {
                'valid': False,
                'warnings': [],
                'errors': [f"Validation error: {str(e)}"]
            }
    
    def _calculate_return_statistics(self, return_series: pd.Series) -> Dict[str, float]:
        """计算收益率统计指标"""
        try:
            return {
                'total_return': return_series.sum(),
                'mean_return': return_series.mean(),
                'std_return': return_series.std(),
                'sharpe_ratio': return_series.mean() / return_series.std() if return_series.std() > 0 else 0,
                'max_return': return_series.max(),
                'min_return': return_series.min(),
                'positive_days': (return_series > 0).sum(),
                'negative_days': (return_series < 0).sum(),
                'win_rate': (return_series > 0).mean()
            }
        except Exception as e:
            logger.error(f"Return statistics calculation failed: {e}")
            return {}
    
    def save_return_data(self, return_results: Dict[str, Any]) -> List[str]:
        """
        保存收益率数据
        
        Args:
            return_results: 收益率计算结果
            
        Returns:
            保存的文件路径列表
        """
        logger.info("Saving return rate data")
        
        try:
            output_config = self.return_config.output
            storage_path = output_config.storage_path
            
            # 创建输出目录
            os.makedirs(storage_path, exist_ok=True)
            
            saved_files = []
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存为CSV格式
            if 'csv' in output_config.file_formats:
                csv_file = os.path.join(storage_path, f'return_rates_{timestamp}.csv')
                self._save_as_csv(return_results, csv_file)
                saved_files.append(csv_file)
            
            # 保存为Excel格式
            if 'excel' in output_config.file_formats:
                excel_file = os.path.join(storage_path, f'return_rates_{timestamp}.xlsx')
                self._save_as_excel(return_results, excel_file)
                saved_files.append(excel_file)
            
            logger.info(f"Return rate data saved to {len(saved_files)} files")
            return saved_files
            
        except Exception as e:
            logger.error(f"Failed to save return rate data: {e}")
            return []
    
    def _save_as_csv(self, return_results: Dict[str, Any], file_path: str):
        """保存为CSV格式"""
        try:
            # 将所有收益率数据合并到一个DataFrame中
            all_data = []

            for category, category_data in return_results.items():
                if isinstance(category_data, dict):
                    for strategy, strategy_data in category_data.items():
                        if 'return_series' in strategy_data:
                            for record in strategy_data['return_series']:
                                all_data.append({
                                    'category': category,
                                    'strategy': strategy,
                                    'date': record['date'],
                                    'return_rate': record['return_rate']
                                })

            if all_data:
                df = pd.DataFrame(all_data)
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
                logger.info(f"Return data saved to CSV: {file_path}")

        except Exception as e:
            logger.error(f"Failed to save CSV file {file_path}: {e}")

    def _save_as_excel(self, return_results: Dict[str, Any], file_path: str):
        """保存为Excel格式"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 为每个类别创建一个工作表
                for category, category_data in return_results.items():
                    if isinstance(category_data, dict):
                        category_df_list = []

                        for strategy, strategy_data in category_data.items():
                            if 'return_series' in strategy_data:
                                strategy_df = pd.DataFrame(strategy_data['return_series'])
                                strategy_df['strategy'] = strategy
                                category_df_list.append(strategy_df)

                        if category_df_list:
                            category_df = pd.concat(category_df_list, ignore_index=True)
                            # 限制工作表名称长度
                            sheet_name = category[:31] if len(category) > 31 else category
                            category_df.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"Return data saved to Excel: {file_path}")

        except Exception as e:
            logger.error(f"Failed to save Excel file {file_path}: {e}")

    def run_comprehensive_return_calculation(self, cta_data: pd.DataFrame,
                                           position_data: pd.DataFrame) -> Dict[str, Any]:
        """
        运行综合收益率计算

        Args:
            cta_data: CTA数据
            position_data: 持仓数据

        Returns:
            综合收益率计算结果
        """
        logger.info("Running comprehensive return rate calculation")

        try:
            results = {}

            # 1. 策略类别收益率
            strategy_returns = self.calculate_strategy_category_returns(cta_data, position_data)
            results['strategy_category_returns'] = strategy_returns

            # 2. 特定期货收益率
            futures_returns = self.calculate_specific_futures_returns(cta_data, position_data)
            results['specific_futures_returns'] = futures_returns

            # 3. 策略信号收益率
            signal_returns = self.calculate_signal_returns(cta_data, position_data)
            results['signal_returns'] = signal_returns

            # 4. 保存收益率数据
            if self.return_config.output.use_replacement_mode:
                saved_files = self.save_return_data(results)
                results['saved_files'] = saved_files

            # 5. 生成验证摘要
            validation_summary = self._generate_validation_summary(results)
            results['validation_summary'] = validation_summary

            logger.info("Comprehensive return rate calculation completed")
            return results

        except Exception as e:
            logger.error(f"Comprehensive return rate calculation failed: {e}")
            raise

    def _generate_validation_summary(self, return_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成验证摘要"""
        try:
            summary = {
                'total_strategies_processed': 0,
                'successful_calculations': 0,
                'failed_calculations': 0,
                'warnings_count': 0,
                'errors_count': 0,
                'missing_position_data': []
            }

            # 统计各类别的验证结果
            for category, category_data in return_results.items():
                if isinstance(category_data, dict) and category != 'saved_files':
                    for strategy, strategy_data in category_data.items():
                        summary['total_strategies_processed'] += 1

                        if 'validation' in strategy_data:
                            validation = strategy_data['validation']
                            if validation.get('valid', False):
                                summary['successful_calculations'] += 1
                            else:
                                summary['failed_calculations'] += 1

                            summary['warnings_count'] += len(validation.get('warnings', []))
                            summary['errors_count'] += len(validation.get('errors', []))

                        if 'error' in strategy_data and 'not found' in strategy_data['error']:
                            summary['missing_position_data'].append(f"{category}: {strategy}")

            return summary

        except Exception as e:
            logger.error(f"Validation summary generation failed: {e}")
            return {}
