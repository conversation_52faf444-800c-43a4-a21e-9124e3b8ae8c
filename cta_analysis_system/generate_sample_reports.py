#!/usr/bin/env python3
"""
生成样本报告脚本

使用实际数据生成样本报告，展示系统的端到端功能
"""

import pandas as pd
import os
from datetime import datetime
from config.modular_config_manager import ModularConfigManager
from analysis.comprehensive_analyzer import ComprehensiveAnalyzer

def load_data():
    """加载数据"""
    print('📊 加载数据...')
    
    encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312', 'latin1']
    
    # 加载CTA数据
    cta_data = None
    for encoding in encodings:
        try:
            cta_data = pd.read_csv('input/processed_cta_data.csv', encoding=encoding)
            print(f'✅ CTA数据加载成功 (编码: {encoding})')
            break
        except UnicodeDecodeError:
            continue
    
    # 加载持仓数据
    position_data = None
    for encoding in encodings:
        try:
            position_data = pd.read_csv('input/strategy_position.csv', encoding=encoding)
            print(f'✅ 持仓数据加载成功 (编码: {encoding})')
            break
        except UnicodeDecodeError:
            continue
    
    if cta_data is None or position_data is None:
        raise ValueError("数据加载失败")
    
    print(f'📈 数据规模: CTA {len(cta_data)} 条, 持仓 {len(position_data)} 条')
    return cta_data, position_data

def run_analysis(cta_data, position_data):
    """运行分析"""
    print('🔍 运行综合分析...')
    
    # 初始化配置和分析器
    config_manager = ModularConfigManager()
    config_manager.initialize()
    master_config = config_manager.get_master_config()
    analyzer = ComprehensiveAnalyzer(master_config)
    
    # 运行分析
    analysis_results = analyzer.run_comprehensive_analysis(cta_data, position_data)
    
    return analysis_results

def generate_simple_markdown_report(analysis_results, output_file):
    """生成简单的Markdown报告"""
    print(f'📝 生成Markdown报告: {output_file}')
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# CTA策略分析系统 - 综合分析报告\n\n")
        f.write(f"**生成时间:** {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}\n\n")
        
        # 分析概览
        f.write("## 📊 分析概览\n\n")
        successful_modules = sum(1 for results in analysis_results.values() 
                               if results and isinstance(results, dict) and len(results) > 0)
        total_modules = len(analysis_results)
        f.write(f"- **分析模块总数:** {total_modules}\n")
        f.write(f"- **成功模块数:** {successful_modules}\n")
        f.write(f"- **成功率:** {successful_modules/total_modules*100:.1f}%\n\n")
        
        # 盈亏分析结果
        if 'pnl_analysis' in analysis_results and analysis_results['pnl_analysis']:
            f.write("## 💰 盈亏分析结果\n\n")
            pnl_results = analysis_results['pnl_analysis']

            # 提取策略类别数据
            if 'classified_by_strategy_category' in pnl_results:
                strategy_data = pnl_results['classified_by_strategy_category']
                if 'summary' in strategy_data and 'primary_field_summary' in strategy_data['summary']:
                    f.write("### 按策略类别分析\n\n")
                    f.write("| 策略类别 | 盈亏金额 (元) | 交易次数 | 平均盈亏 (元) |\n")
                    f.write("|---------|-------------|---------|-------------|\n")

                    primary_summary = strategy_data['summary']['primary_field_summary']
                    total_pnl = 0
                    for strategy, stats in primary_summary.items():
                        pnl_sum = stats['sum']
                        count = stats['count']
                        mean_pnl = stats['mean']
                        f.write(f"| {strategy} | {pnl_sum:,.2f} | {count:,} | {mean_pnl:.2f} |\n")
                        total_pnl += pnl_sum

                    f.write(f"| **总计** | **{total_pnl:,.2f}** | **{strategy_data['summary']['record_count']:,}** | **{total_pnl/strategy_data['summary']['record_count']:.2f}** |\n\n")

            # 提取信号频率数据
            if 'classified_by_signal_freq' in pnl_results:
                freq_data = pnl_results['classified_by_signal_freq']
                if 'summary' in freq_data and 'primary_field_summary' in freq_data['summary']:
                    f.write("### 按信号频率分析\n\n")
                    f.write("| 信号频率 | 盈亏金额 (元) | 交易次数 | 平均盈亏 (元) |\n")
                    f.write("|---------|-------------|---------|-------------|\n")

                    freq_summary = freq_data['summary']['primary_field_summary']
                    for freq, stats in freq_summary.items():
                        pnl_sum = stats['sum']
                        count = stats['count']
                        mean_pnl = stats['mean']
                        f.write(f"| {freq} | {pnl_sum:,.2f} | {count:,} | {mean_pnl:.2f} |\n")
                    f.write("\n")
        
        # 收益率分析结果
        if 'return_rate_analysis' in analysis_results and analysis_results['return_rate_analysis']:
            f.write("## 📈 收益率分析结果\n\n")
            return_results = analysis_results['return_rate_analysis']
            
            if 'strategy_category_returns' in return_results:
                f.write("### 策略类别收益率\n\n")
                f.write("| 策略类别 | 总收益率 | 年化收益率 | 夏普比率 |\n")
                f.write("|---------|---------|-----------|----------|\n")
                
                for strategy, data in return_results['strategy_category_returns'].items():
                    if isinstance(data, dict):
                        total_return = data.get('total_return', 0)
                        annualized_return = data.get('annualized_return', 0)
                        sharpe_ratio = data.get('sharpe_ratio', 0)
                        f.write(f"| {strategy} | {total_return:.4f} | {annualized_return:.4f} | {sharpe_ratio:.4f} |\n")
                f.write("\n")
        
        # 风险分析结果
        if 'risk_analysis' in analysis_results and analysis_results['risk_analysis']:
            f.write("## ⚠️ 风险分析结果\n\n")
            risk_results = analysis_results['risk_analysis']
            
            if 'basic_risk_metrics' in risk_results:
                f.write("### 基础风险指标\n\n")
                f.write("| 策略 | 波动率 | 最大回撤 | VaR (95%) | ES (95%) |\n")
                f.write("|------|--------|----------|-----------|----------|\n")
                
                basic_metrics = risk_results['basic_risk_metrics']
                for strategy, metrics in basic_metrics.items():
                    if isinstance(metrics, dict):
                        volatility = metrics.get('volatility', 0)
                        max_drawdown = metrics.get('max_drawdown', 0)
                        var_95 = metrics.get('var_95', 0)
                        es_95 = metrics.get('es_95', 0)
                        f.write(f"| {strategy} | {volatility:.4f} | {max_drawdown:.4f} | {var_95:.4f} | {es_95:.4f} |\n")
                f.write("\n")
        
        # 贡献分析结果
        if 'contribution_analysis' in analysis_results and analysis_results['contribution_analysis']:
            f.write("## 🎯 贡献分析结果\n\n")
            contrib_results = analysis_results['contribution_analysis']

            # 显示最新的贡献分析（通常是daily）
            if 'daily' in contrib_results:
                daily_contrib = contrib_results['daily']
                if 'strategy_contributions' in daily_contrib:
                    f.write("### 策略贡献分析 (当日)\n\n")
                    f.write("| 策略 | 盈亏金额 (元) | 贡献百分比 |\n")
                    f.write("|------|---------------|------------|\n")

                    strategy_contrib = daily_contrib['strategy_contributions']
                    for strategy, data in strategy_contrib.items():
                        if isinstance(data, dict):
                            pnl = data.get('pnl', 0)
                            percentage = data.get('percentage', 0)
                            f.write(f"| {strategy} | {pnl:,.2f} | {percentage:.2f}% |\n")

                    # 显示总计
                    total_pnl = daily_contrib.get('total_pnl', 0)
                    total_gain = daily_contrib.get('total_gain', 0)
                    f.write(f"\n**总盈亏:** {total_pnl:,.2f} 元  \n")
                    f.write(f"**总盈利:** {total_gain:,.2f} 元\n\n")
        
        # 系统状态
        f.write("## 🔧 系统状态\n\n")
        f.write("### 模块运行状态\n\n")
        f.write("| 模块 | 状态 | 结果数量 |\n")
        f.write("|------|------|----------|\n")
        
        for module_name, results in analysis_results.items():
            if results and isinstance(results, dict) and len(results) > 0:
                status = "✅ 成功"
                result_count = len(results)
            else:
                status = "❌ 失败"
                result_count = 0
            f.write(f"| {module_name} | {status} | {result_count} |\n")
        
        f.write("\n")
        f.write("---\n")
        f.write("*报告由CTA策略分析系统自动生成*\n")
    
    return output_file

def generate_simple_html_report(analysis_results, output_file):
    """生成简单的HTML报告"""
    print(f'🌐 生成HTML报告: {output_file}')
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTA策略分析系统 - 综合分析报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .metric { display: inline-block; margin: 10px; padding: 15px; background: #f8f9fa; border-radius: 5px; min-width: 150px; }
        .metric-value { font-size: 1.5em; font-weight: bold; color: #007bff; }
    </style>
</head>
<body>
""")
        
        # 页面头部
        f.write(f"""
    <div class="header">
        <h1>🏦 CTA策略分析系统 - 综合分析报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
    </div>
""")
        
        # 分析概览
        successful_modules = sum(1 for results in analysis_results.values() 
                               if results and isinstance(results, dict) and len(results) > 0)
        total_modules = len(analysis_results)
        
        f.write(f"""
    <div class="section">
        <h2>📊 分析概览</h2>
        <div class="metric">
            <div>分析模块总数</div>
            <div class="metric-value">{total_modules}</div>
        </div>
        <div class="metric">
            <div>成功模块数</div>
            <div class="metric-value">{successful_modules}</div>
        </div>
        <div class="metric">
            <div>成功率</div>
            <div class="metric-value">{successful_modules/total_modules*100:.1f}%</div>
        </div>
    </div>
""")
        
        # 盈亏分析结果
        if 'pnl_analysis' in analysis_results and analysis_results['pnl_analysis']:
            f.write("""
    <div class="section">
        <h2>💰 盈亏分析结果</h2>
""")
            pnl_results = analysis_results['pnl_analysis']

            # 提取策略类别数据
            if 'classified_by_strategy_category' in pnl_results:
                strategy_data = pnl_results['classified_by_strategy_category']
                if 'summary' in strategy_data and 'primary_field_summary' in strategy_data['summary']:
                    f.write("""
        <h3>按策略类别分析</h3>
        <table>
            <tr><th>策略类别</th><th>盈亏金额 (元)</th><th>交易次数</th><th>平均盈亏 (元)</th></tr>
""")
                    primary_summary = strategy_data['summary']['primary_field_summary']
                    total_pnl = 0
                    for strategy, stats in primary_summary.items():
                        pnl_sum = stats['sum']
                        count = stats['count']
                        mean_pnl = stats['mean']
                        color_class = "success" if pnl_sum > 0 else "error"
                        f.write(f'            <tr><td>{strategy}</td><td class="{color_class}">{pnl_sum:,.2f}</td><td>{count:,}</td><td>{mean_pnl:.2f}</td></tr>\n')
                        total_pnl += pnl_sum

                    total_color_class = "success" if total_pnl > 0 else "error"
                    total_count = strategy_data['summary']['record_count']
                    avg_pnl = total_pnl / total_count if total_count > 0 else 0
                    f.write(f'            <tr style="font-weight: bold;"><td>总计</td><td class="{total_color_class}">{total_pnl:,.2f}</td><td>{total_count:,}</td><td>{avg_pnl:.2f}</td></tr>\n')
                    f.write("        </table>\n")

            f.write("    </div>\n")
        
        # 系统状态
        f.write("""
    <div class="section">
        <h2>🔧 系统状态</h2>
        <h3>模块运行状态</h3>
        <table>
            <tr><th>模块</th><th>状态</th><th>结果数量</th></tr>
""")
        
        for module_name, results in analysis_results.items():
            if results and isinstance(results, dict) and len(results) > 0:
                status = '<span class="success">✅ 成功</span>'
                result_count = len(results)
            else:
                status = '<span class="error">❌ 失败</span>'
                result_count = 0
            f.write(f"            <tr><td>{module_name}</td><td>{status}</td><td>{result_count}</td></tr>\n")
        
        f.write("""        </table>
    </div>
    
    <div style="text-align: center; margin-top: 30px; color: #666;">
        <p><em>报告由CTA策略分析系统自动生成</em></p>
    </div>
</body>
</html>
""")
    
    return output_file

def main():
    """主函数"""
    print('🚀 CTA策略分析系统 - 样本报告生成')
    print('=' * 60)
    
    try:
        # 1. 加载数据
        cta_data, position_data = load_data()
        
        # 2. 运行分析
        analysis_results = run_analysis(cta_data, position_data)
        
        # 3. 生成报告
        print()
        print('📄 生成报告...')
        
        # Markdown报告
        markdown_file = generate_simple_markdown_report(
            analysis_results, 
            'output/reports/markdown/comprehensive_analysis_report.md'
        )
        
        # HTML报告
        html_file = generate_simple_html_report(
            analysis_results,
            'output/reports/html/comprehensive_analysis_report.html'
        )
        
        # 4. 显示结果
        print()
        print('✅ 报告生成完成!')
        print(f'📝 Markdown报告: {markdown_file}')
        print(f'🌐 HTML报告: {html_file}')
        
        if os.path.exists(markdown_file):
            print(f'   Markdown文件大小: {os.path.getsize(markdown_file)} 字节')
        
        if os.path.exists(html_file):
            print(f'   HTML文件大小: {os.path.getsize(html_file)} 字节')
        
        # 5. 显示关键数据
        print()
        print('📊 关键分析结果:')
        
        # 显示盈亏汇总
        if 'pnl_analysis' in analysis_results and 'strategy_category' in analysis_results['pnl_analysis']:
            total_pnl = sum(analysis_results['pnl_analysis']['strategy_category'].values())
            print(f'💰 总盈亏: {total_pnl:,.2f} 元')
        
        # 显示成功率
        successful_modules = sum(1 for results in analysis_results.values() 
                               if results and isinstance(results, dict) and len(results) > 0)
        total_modules = len(analysis_results)
        print(f'🎯 分析成功率: {successful_modules}/{total_modules} ({successful_modules/total_modules*100:.1f}%)')
        
        print()
        print('🎉 样本报告生成任务完成!')
        
    except Exception as e:
        print(f'❌ 报告生成失败: {e}')
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
